[package]
name = "iec-61131-3-lsp"
authors = ["<PERSON><PERSON><PERSON>"]
license = "GPL-3.0"
description = ""
version = "0.1.0"
edition = "2021"
publish = false

[workspace]
members = [
    "crates/ast",
    "crates/db",
    "crates/server",
    "crates/tree-sitter",
    "vscode/server",
]

[workspace.dependencies]
auto-lsp = { git = "https://github.com/adclz/auto-lsp", rev = "8d639be29c20302400b8e84921d980c4cd129e6a" }
auto-lsp-codegen = { git = "https://github.com/adclz/auto-lsp", rev = "8d639be29c20302400b8e84921d980c4cd129e6a" }
log = "0.4.27"
dashmap = "6.1.0"
rustc-hash = "2.1.0"
topiary-core = "0.6.1"
ast = { path = "crates/ast" }
tree-sitter-iec-61131-3 = { path = "crates/tree-sitter" }
server = { path = "crates/server" }
db = { path = "crates/db" }
bon = "3.6.4"
phf = { version = "0.11.3", features = ["macros"] }
bitflags = "2.9.1"
auto_enums = "0.8.7"