#![allow(deprecated)]

use std::sync::Arc;

use auto_lsp::{
    anyhow,
    core::ast::AstNode,
    default::db::{file::File, tracked::ParsedAst, BaseDatabase},
    lsp_types::{self, CompletionItem, CompletionParams, CompletionResponse},
};
use db::{solver::namespace::namespaces_in_file, to_proto::IterToProto};

const COMPLETION_MARKER: &str = "iecCompletionMarker";

pub fn completions(
    db: &impl BaseDatabase,
    params: CompletionParams,
) -> anyhow::Result<Option<CompletionResponse>> {
    let uri = &params.text_document_position.text_document.uri;

    let file = db
        .get_file(uri)
        .ok_or_else(|| anyhow::format_err!("File not found in workspace"))?;

    let doc = file.document(db);

    let position = params.text_document_position.position;
    let offset = match doc.offset_at(position) {
        Some(offset) => offset,
        None => return Ok(None),
    };

    let ns = namespaces_in_file(db, file).unwrap();
    if let Some(symbol) = ns.descendant_at(db, offset) {
        if let Some(ctx) = symbol.completion_ctx(db, offset) {
            return Ok(Some(CompletionResponse::Array(ctx)));
        }
    }

    let mut results = vec![];

    Ok(Some(CompletionResponse::Array(results)))
}
