[package]
name = "server"
authors = ["<PERSON><PERSON><PERSON>"]
license = "GPL-3.0"
description = ""
version = "0.1.0"
edition = "2021"
publish = false

[dependencies]
auto-lsp = { workspace = true, features = ["lsp_server"] }
db = { workspace = true }
log = { workspace = true }
tree-sitter-iec-61131-3 = { workspace = true }
topiary-core = { workspace = true }
serde_json = "1.0.109"
stderrlog = "0.6.0"
