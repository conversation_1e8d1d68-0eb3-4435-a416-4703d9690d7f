use bitflags::bitflags;

bitflags! {
    #[repr(transparent)]
    #[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Eq, Hash)]
    pub struct Modifiers: u16 {
        const PUBLIC = 1 << 0;
        const PROTECTED = 1 << 1;
        const INTERNAL = 1 << 2;
        const PRIVATE = 1 << 3;

        const ABSTRACT = 1 << 4;
        const FINAL = 1 << 5;
        const OVERRIDE = 1 << 6;
    }
}