{"$schema": "https://tree-sitter.github.io/tree-sitter/assets/schemas/grammar.schema.json", "name": "iec_61131_3", "word": "identifier", "rules": {"source_file": {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "config_decl"}, {"type": "SYMBOL", "name": "prog_decl"}, {"type": "SYMBOL", "name": "namespace_decl"}, {"type": "SYMBOL", "name": "data_type_decl"}, {"type": "SYMBOL", "name": "func_decl"}, {"type": "SYMBOL", "name": "fb_decl"}, {"type": "SYMBOL", "name": "class_decl"}, {"type": "SYMBOL", "name": "interface_decl"}]}}, "comment": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "//"}, {"type": "PATTERN", "value": "[^\\r\\n]*"}]}, {"type": "SEQ", "members": [{"type": "STRING", "value": "(*"}, {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "PATTERN", "value": "[^*]"}, {"type": "PATTERN", "value": "\\*[^)]"}]}}, {"type": "STRING", "value": "*)"}]}, {"type": "SEQ", "members": [{"type": "STRING", "value": "/*"}, {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "PATTERN", "value": "[^*]"}, {"type": "PATTERN", "value": "\\*[^/]"}]}}, {"type": "STRING", "value": "*/"}]}]}, "pragma": {"type": "SEQ", "members": [{"type": "STRING", "value": "{"}, {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "PATTERN", "value": "[^}]"}, {"type": "PATTERN", "value": "}[^}]"}]}}, {"type": "STRING", "value": "}"}]}, "constant": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "numeric_literal"}, {"type": "SYMBOL", "name": "char_literal"}, {"type": "SYMBOL", "name": "time_literal"}, {"type": "SYMBOL", "name": "bool_literal"}]}, "numeric_literal": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "int_literal"}, {"type": "SYMBOL", "name": "real_literal"}]}, "int_literal": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "FIELD", "name": "kind", "content": {"type": "SYMBOL", "name": "int_kind"}}, {"type": "STRING", "value": "#"}]}, {"type": "BLANK"}]}, {"type": "FIELD", "name": "int", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "signed_int"}, {"type": "SYMBOL", "name": "binary_int"}, {"type": "SYMBOL", "name": "octal_int"}, {"type": "SYMBOL", "name": "hex_int"}]}}]}, "int_kind": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "multibits_type_name"}, {"type": "SYMBOL", "name": "int_type_name"}]}, "unsigned_int": {"type": "PATTERN", "value": "[0-9]+(_[0-9]+)*"}, "signed_int": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "+"}, {"type": "STRING", "value": "-"}]}, {"type": "BLANK"}]}, {"type": "SYMBOL", "name": "unsigned_int"}]}, "binary_int": {"type": "SEQ", "members": [{"type": "STRING", "value": "2#"}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "_bit"}}]}, "octal_int": {"type": "SEQ", "members": [{"type": "STRING", "value": "8#"}, {"type": "FIELD", "name": "value", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "_octal_digit"}, {"type": "REPEAT1", "content": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "_"}, {"type": "BLANK"}]}, {"type": "SYMBOL", "name": "_octal_digit"}]}}]}}]}, "hex_int": {"type": "SEQ", "members": [{"type": "STRING", "value": "16#"}, {"type": "FIELD", "name": "value", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "_hex_digit"}, {"type": "REPEAT1", "content": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "_"}, {"type": "BLANK"}]}, {"type": "SYMBOL", "name": "_hex_digit"}]}}]}}]}, "real_literal": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "real"}, {"type": "SYMBOL", "name": "l_real"}]}, "real": {"type": "SEQ", "members": [{"type": "STRING", "value": "REAL"}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "real_value"}}]}, "l_real": {"type": "SEQ", "members": [{"type": "STRING", "value": "LREAL"}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "real_value"}}]}, "real_value": {"type": "PATTERN", "value": "[0-9]+(_[0-9]+)*"}, "bool_literal": {"type": "SEQ", "members": [{"type": "FIELD", "name": "type", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "BOOL#"}, {"type": "BLANK"}]}}, {"type": "FIELD", "name": "value", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "TRUE"}, {"type": "STRING", "value": "FALSE"}]}}]}, "char_literal": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "STRING#"}, {"type": "BLANK"}]}, {"type": "FIELD", "name": "char", "content": {"type": "SYMBOL", "name": "char_str"}}]}, "char_str": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_s_byte_char_str"}, {"type": "SYMBOL", "name": "_d_byte_char_str"}]}, "_s_byte_char_str": {"type": "SEQ", "members": [{"type": "STRING", "value": "'"}, {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "_s_byte_char_value"}}, {"type": "STRING", "value": "'"}]}, "_d_byte_char_str": {"type": "SEQ", "members": [{"type": "STRING", "value": "\""}, {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "_d_byte_char_value"}}, {"type": "STRING", "value": "\""}]}, "_s_byte_char_value": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_common_char_value"}, {"type": "STRING", "value": "$'"}, {"type": "STRING", "value": "\""}, {"type": "SEQ", "members": [{"type": "STRING", "value": "$"}, {"type": "SYMBOL", "name": "_hex_digit"}, {"type": "SYMBOL", "name": "_hex_digit"}]}]}, "_d_byte_char_value": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_common_char_value"}, {"type": "STRING", "value": "'"}, {"type": "STRING", "value": "$\""}, {"type": "SEQ", "members": [{"type": "STRING", "value": "$"}, {"type": "SYMBOL", "name": "_hex_digit"}, {"type": "SYMBOL", "name": "_hex_digit"}, {"type": "SYMBOL", "name": "_hex_digit"}, {"type": "SYMBOL", "name": "_hex_digit"}]}]}, "_common_char_value": {"type": "CHOICE", "members": [{"type": "STRING", "value": " "}, {"type": "STRING", "value": "!"}, {"type": "STRING", "value": "#"}, {"type": "STRING", "value": "%"}, {"type": "STRING", "value": "&"}, {"type": "STRING", "value": "("}, {"type": "STRING", "value": ")"}, {"type": "STRING", "value": "*"}, {"type": "STRING", "value": "+"}, {"type": "STRING", "value": ","}, {"type": "STRING", "value": "-"}, {"type": "STRING", "value": "."}, {"type": "STRING", "value": "/"}, {"type": "STRING", "value": "0"}, {"type": "STRING", "value": "1"}, {"type": "STRING", "value": "2"}, {"type": "STRING", "value": "0"}, {"type": "STRING", "value": "1"}, {"type": "STRING", "value": "2"}, {"type": "STRING", "value": "3"}, {"type": "STRING", "value": "4"}, {"type": "STRING", "value": "5"}, {"type": "STRING", "value": "6"}, {"type": "STRING", "value": "7"}, {"type": "STRING", "value": "8"}, {"type": "STRING", "value": "9"}, {"type": "STRING", "value": ":"}, {"type": "STRING", "value": ";"}, {"type": "STRING", "value": "<"}, {"type": "STRING", "value": "="}, {"type": "STRING", "value": ">"}, {"type": "STRING", "value": "?"}, {"type": "STRING", "value": "@"}, {"type": "STRING", "value": "A"}, {"type": "STRING", "value": "B"}, {"type": "STRING", "value": "C"}, {"type": "STRING", "value": "D"}, {"type": "STRING", "value": "E"}, {"type": "STRING", "value": "F"}, {"type": "STRING", "value": "G"}, {"type": "STRING", "value": "H"}, {"type": "STRING", "value": "I"}, {"type": "STRING", "value": "J"}, {"type": "STRING", "value": "K"}, {"type": "STRING", "value": "L"}, {"type": "STRING", "value": "M"}, {"type": "STRING", "value": "N"}, {"type": "STRING", "value": "O"}, {"type": "STRING", "value": "P"}, {"type": "STRING", "value": "Q"}, {"type": "STRING", "value": "R"}, {"type": "STRING", "value": "S"}, {"type": "STRING", "value": "T"}, {"type": "STRING", "value": "U"}, {"type": "STRING", "value": "V"}, {"type": "STRING", "value": "W"}, {"type": "STRING", "value": "X"}, {"type": "STRING", "value": "Y"}, {"type": "STRING", "value": "Z"}, {"type": "STRING", "value": "["}, {"type": "STRING", "value": "\\"}, {"type": "STRING", "value": "]"}, {"type": "STRING", "value": "^"}, {"type": "STRING", "value": "_"}, {"type": "STRING", "value": "`"}, {"type": "STRING", "value": "a"}, {"type": "STRING", "value": "b"}, {"type": "STRING", "value": "c"}, {"type": "STRING", "value": "d"}, {"type": "STRING", "value": "e"}, {"type": "STRING", "value": "f"}, {"type": "STRING", "value": "g"}, {"type": "STRING", "value": "h"}, {"type": "STRING", "value": "i"}, {"type": "STRING", "value": "j"}, {"type": "STRING", "value": "k"}, {"type": "STRING", "value": "l"}, {"type": "STRING", "value": "m"}, {"type": "STRING", "value": "n"}, {"type": "STRING", "value": "o"}, {"type": "STRING", "value": "p"}, {"type": "STRING", "value": "q"}, {"type": "STRING", "value": "r"}, {"type": "STRING", "value": "s"}, {"type": "STRING", "value": "t"}, {"type": "STRING", "value": "u"}, {"type": "STRING", "value": "v"}, {"type": "STRING", "value": "w"}, {"type": "STRING", "value": "x"}, {"type": "STRING", "value": "y"}, {"type": "STRING", "value": "z"}, {"type": "STRING", "value": "{"}, {"type": "STRING", "value": "|"}, {"type": "STRING", "value": "}"}, {"type": "STRING", "value": "~"}, {"type": "STRING", "value": "$$"}, {"type": "STRING", "value": "$L"}, {"type": "STRING", "value": "$N"}, {"type": "STRING", "value": "$P"}, {"type": "STRING", "value": "$R"}, {"type": "STRING", "value": "$T"}]}, "time_literal": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "duration"}, {"type": "SYMBOL", "name": "time_of_day"}, {"type": "SYMBOL", "name": "date"}, {"type": "SYMBOL", "name": "date_and_time"}]}, "duration": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "time"}, {"type": "SYMBOL", "name": "ltime"}]}, "time": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "T"}, {"type": "STRING", "value": "TIME"}]}, {"type": "STRING", "value": "#"}, {"type": "FIELD", "name": "sign", "content": {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "+"}, {"type": "STRING", "value": "-"}]}, {"type": "BLANK"}]}}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "time_value"}}]}, "ltime": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "LT"}, {"type": "STRING", "value": "LTIME"}]}, {"type": "STRING", "value": "#"}, {"type": "FIELD", "name": "sign", "content": {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "+"}, {"type": "STRING", "value": "-"}]}, {"type": "BLANK"}]}}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "time_value"}}]}, "time_value": {"type": "PATTERN", "value": "([0-9._]+(d|h|ms|ns|m|s))+"}, "fix_point": {"type": "SEQ", "members": [{"type": "FIELD", "name": "real", "content": {"type": "SYMBOL", "name": "unsigned_int"}}, {"type": "STRING", "value": "."}, {"type": "FIELD", "name": "frac", "content": {"type": "SYMBOL", "name": "unsigned_int"}}]}, "time_of_day": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "tod"}, {"type": "SYMBOL", "name": "ltod"}]}, "tod": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "TOD"}, {"type": "STRING", "value": "TIME_OF_DAY"}]}, {"type": "STRING", "value": "#"}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "daytime"}}]}, "ltod": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "LTOD"}, {"type": "STRING", "value": "LTIME_OF_DAY"}]}, {"type": "STRING", "value": "#"}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "daytime"}}]}, "daytime": {"type": "PATTERN", "value": "[0-9a-zA-Z_.:]+"}, "date": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "short_date"}, {"type": "SYMBOL", "name": "long_date"}]}, "short_date": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "D"}, {"type": "STRING", "value": "DATE"}]}, {"type": "STRING", "value": "#"}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "date_literal"}}]}, "long_date": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "LD"}, {"type": "STRING", "value": "LDATE"}]}, {"type": "STRING", "value": "#"}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "date_literal"}}]}, "date_literal": {"type": "PATTERN", "value": "[0-9a-zA-Z_.:-]+"}, "date_and_time": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "short_date_and_time"}, {"type": "SYMBOL", "name": "long_date_and_time"}]}, "short_date_and_time": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "DT"}, {"type": "STRING", "value": "DATE_AND_TIME"}]}, {"type": "STRING", "value": "#"}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "date_and_daytime"}}]}, "long_date_and_time": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "LDT"}, {"type": "STRING", "value": "LDATE_AND_TIME"}]}, {"type": "STRING", "value": "#"}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "date_and_daytime"}}]}, "date_and_daytime": {"type": "PATTERN", "value": "[0-9dhmsDHMS_.]+"}, "_data_type_access": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "fq_name"}, {"type": "SYMBOL", "name": "_elem_type_name"}]}, "_elem_type_name": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "numeric_type_name"}, {"type": "SYMBOL", "name": "bit_str_type_name"}, {"type": "SYMBOL", "name": "date_type_name"}, {"type": "SYMBOL", "name": "time_type_name"}, {"type": "SYMBOL", "name": "tod_type_name"}, {"type": "SYMBOL", "name": "dt_type_name"}]}, "numeric_type_name": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "int_type_name"}, {"type": "SYMBOL", "name": "real_type_name"}]}, "int_type_name": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "sign_int_type_name"}, {"type": "SYMBOL", "name": "unsign_int_type_name"}]}, "sign_int_type_name": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "STRING", "value": "SINT"}, "named": true, "value": "sint_name"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "INT"}, "named": true, "value": "int_name"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "DINT"}, "named": true, "value": "dint_name"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "LINT"}, "named": true, "value": "lint_name"}]}, "unsign_int_type_name": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "STRING", "value": "USINT"}, "named": true, "value": "usint_name"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "UINT"}, "named": true, "value": "uint_name"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "UDINT"}, "named": true, "value": "udint_name"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "ULINT"}, "named": true, "value": "ulint_name"}]}, "real_type_name": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "STRING", "value": "REAL"}, "named": true, "value": "real_name"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "LREAL"}, "named": true, "value": "lreal_name"}]}, "string_type_name": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "STRING"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "["}, {"type": "SYMBOL", "name": "unsigned_int"}, {"type": "STRING", "value": "]"}]}, {"type": "BLANK"}]}]}, {"type": "SEQ", "members": [{"type": "STRING", "value": "WSTRING"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "["}, {"type": "SYMBOL", "name": "unsigned_int"}, {"type": "STRING", "value": "]"}]}, {"type": "BLANK"}]}]}, {"type": "STRING", "value": "CHAR"}, {"type": "STRING", "value": "WCHAR"}]}, "time_type_name": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "STRING", "value": "TIME"}, "named": true, "value": "time_name"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "LTIME"}, "named": true, "value": "ltime_name"}]}, "date_type_name": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "STRING", "value": "DATE"}, "named": true, "value": "date_name"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "LDATE"}, "named": true, "value": "ldate_name"}]}, "tod_type_name": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "TOD"}, {"type": "STRING", "value": "TIME_OF_DAY"}]}, "named": true, "value": "tod_name"}, {"type": "ALIAS", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "LTOD"}, {"type": "STRING", "value": "LTIME_OF_DAY"}]}, "named": true, "value": "ltod_name"}]}, "dt_type_name": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "DT"}, {"type": "STRING", "value": "DATE_AND_TIME"}]}, "named": true, "value": "dt_name"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "LDT"}, "named": true, "value": "ldt_name"}]}, "bit_str_type_name": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "STRING", "value": "BOOL"}, "named": true, "value": "bool_name"}, {"type": "SYMBOL", "name": "multibits_type_name"}]}, "multibits_type_name": {"type": "CHOICE", "members": [{"type": "ALIAS", "content": {"type": "STRING", "value": "BYTE"}, "named": true, "value": "byte_name"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "WORD"}, "named": true, "value": "word_name"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "DWORD"}, "named": true, "value": "dword_name"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "LWORD"}, "named": true, "value": "lword_name"}]}, "data_type_decl": {"type": "SEQ", "members": [{"type": "STRING", "value": "TYPE"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "type_decl"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}, {"type": "STRING", "value": "END_TYPE"}]}, "type_decl": {"type": "SEQ", "members": [{"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "SEQ", "members": [{"type": "FIELD", "name": "spec", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "simple_type_spec"}, {"type": "SYMBOL", "name": "subrange_type_spec"}, {"type": "SYMBOL", "name": "enum_type_spec"}, {"type": "SYMBOL", "name": "array_type_spec"}, {"type": "SYMBOL", "name": "struct_type_spec"}, {"type": "SYMBOL", "name": "str_type_spec"}, {"type": "SYMBOL", "name": "ref_type_spec"}]}}, {"type": "FIELD", "name": "init", "content": {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "simple_type_init"}, {"type": "SYMBOL", "name": "array_type_init"}, {"type": "SYMBOL", "name": "struct_type_init"}]}, {"type": "BLANK"}]}}]}]}, "simple_type_spec": {"type": "SEQ", "members": [{"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "_elem_type_name"}]}, "simple_type_init": {"type": "SEQ", "members": [{"type": "STRING", "value": ":="}, {"type": "SYMBOL", "name": "constant_expr"}]}, "subrange_type_spec": {"type": "SEQ", "members": [{"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "type", "content": {"type": "SYMBOL", "name": "int_type_name"}}, {"type": "STRING", "value": "("}, {"type": "FIELD", "name": "range", "content": {"type": "SYMBOL", "name": "subrange"}}, {"type": "STRING", "value": ")"}]}, "subrange_type_init": {"type": "SYMBOL", "name": "simple_type_init"}, "subrange": {"type": "SEQ", "members": [{"type": "FIELD", "name": "lower", "content": {"type": "SYMBOL", "name": "constant_expr"}}, {"type": "STRING", "value": ".."}, {"type": "FIELD", "name": "upper", "content": {"type": "SYMBOL", "name": "constant_expr"}}]}, "enum_type_spec": {"type": "SEQ", "members": [{"type": "STRING", "value": ":"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "_elem_type_name"}, {"type": "BLANK"}]}, {"type": "SYMBOL", "name": "named_spec"}]}, {"type": "SYMBOL", "name": "enum_spec"}]}]}, "enum_type_init": {"type": "SEQ", "members": [{"type": "STRING", "value": ":="}, {"type": "SYMBOL", "name": "enum_value"}]}, "enum_spec": {"type": "PREC_LEFT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "identifier"}]}}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": ")"}]}, {"type": "SYMBOL", "name": "fq_name"}]}]}}, "named_spec": {"type": "PREC_LEFT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "enum_value_spec"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "enum_value_spec"}]}}]}, {"type": "STRING", "value": ")"}]}}, "array_type_spec": {"type": "SEQ", "members": [{"type": "STRING", "value": ":"}, {"type": "STRING", "value": "ARRAY"}, {"type": "STRING", "value": "["}, {"type": "FIELD", "name": "ranges", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "subrange"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "subrange"}]}}]}}, {"type": "STRING", "value": "]"}, {"type": "STRING", "value": "OF"}, {"type": "FIELD", "name": "type", "content": {"type": "SYMBOL", "name": "_data_type_access"}}]}, "array_type_init": {"type": "SEQ", "members": [{"type": "STRING", "value": ":="}, {"type": "STRING", "value": "["}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "array_elem_init"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "array_elem_init"}]}}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": "]"}]}, "struct_type_spec": {"type": "SEQ", "members": [{"type": "STRING", "value": ":"}, {"type": "STRING", "value": "STRUCT"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": "OVERLAP"}, {"type": "BLANK"}]}, {"type": "REPEAT1", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "struct_elem_decl"}, {"type": "STRING", "value": ";"}]}}, {"type": "STRING", "value": "END_STRUCT"}]}, "struct_type_init": {"type": "SEQ", "members": [{"type": "STRING", "value": ":="}, {"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "struct_elem_init"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "struct_elem_init"}]}}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": ")"}]}, "str_type_spec": {"type": "SEQ", "members": [{"type": "STRING", "value": ":"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "s_byte_str_spec"}, {"type": "SYMBOL", "name": "d_byte_str_spec"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "CHAR"}, "named": true, "value": "s_char"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "WCHAR"}, "named": true, "value": "d_char"}]}]}, "str_type_init": {"type": "SEQ", "members": [{"type": "STRING", "value": ":="}, {"type": "SYMBOL", "name": "char_str"}]}, "s_byte_str_spec": {"type": "SEQ", "members": [{"type": "STRING", "value": "STRING"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "["}, {"type": "FIELD", "name": "size", "content": {"type": "SYMBOL", "name": "unsigned_int"}}, {"type": "STRING", "value": "]"}]}, {"type": "BLANK"}]}]}, "d_byte_str_spec": {"type": "SEQ", "members": [{"type": "STRING", "value": "WSTRING"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "["}, {"type": "FIELD", "name": "size", "content": {"type": "SYMBOL", "name": "unsigned_int"}}, {"type": "STRING", "value": "]"}]}, {"type": "BLANK"}]}]}, "enum_value_spec": {"type": "PREC_RIGHT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": ":="}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "int_literal"}, {"type": "SYMBOL", "name": "_expression"}]}]}, {"type": "BLANK"}]}]}}, "enum_value": {"type": "SEQ", "members": [{"type": "STRING", "value": "#"}, {"type": "SYMBOL", "name": "identifier"}]}, "array_elem_init": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "array_elem_init_value"}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "unsigned_int"}, {"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "array_elem_init_value"}, {"type": "BLANK"}]}, {"type": "STRING", "value": ")"}]}]}, "array_elem_init_value": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "constant_expr"}, {"type": "SYMBOL", "name": "struct_elem_init"}, {"type": "SYMBOL", "name": "array_type_init"}]}, "struct_decl": {"type": "SEQ", "members": [{"type": "STRING", "value": "STRUCT"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": "OVERLAP"}, {"type": "BLANK"}]}, {"type": "REPEAT1", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "struct_elem_decl"}, {"type": "STRING", "value": ";"}]}}, {"type": "STRING", "value": "END_STRUCT"}]}, "struct_elem_decl": {"type": "SEQ", "members": [{"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "located_at"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "multibit_part_access"}, {"type": "BLANK"}]}]}, {"type": "BLANK"}]}, {"type": "SEQ", "members": [{"type": "FIELD", "name": "spec", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "simple_type_spec"}, {"type": "SYMBOL", "name": "subrange_type_spec"}, {"type": "SYMBOL", "name": "enum_type_spec"}, {"type": "SYMBOL", "name": "array_type_spec"}, {"type": "SYMBOL", "name": "struct_type_spec"}]}}, {"type": "FIELD", "name": "init", "content": {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "simple_type_init"}, {"type": "SYMBOL", "name": "array_type_init"}, {"type": "SYMBOL", "name": "struct_type_init"}]}, {"type": "BLANK"}]}}]}]}, "struct_init": {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "struct_elem_init"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "struct_elem_init"}]}}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": ")"}]}, "struct_elem_init": {"type": "SEQ", "members": [{"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "STRING", "value": ":="}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "constant_expr"}, {"type": "SYMBOL", "name": "array_type_init"}, {"type": "SYMBOL", "name": "struct_init"}]}]}, "direct_variable": {"type": "PREC_LEFT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "%"}, {"type": "FIELD", "name": "kind", "content": {"type": "SYMBOL", "name": "IQM"}}, {"type": "FIELD", "name": "size", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "XBWDL"}, {"type": "BLANK"}]}}, {"type": "FIELD", "name": "offset", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "unsigned_int"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "."}, {"type": "SYMBOL", "name": "unsigned_int"}]}}]}}]}}, "ref_type_spec": {"type": "SEQ", "members": [{"type": "STRING", "value": ":"}, {"type": "STRING", "value": "REF_TO"}, {"type": "SYMBOL", "name": "_data_type_access"}]}, "ref_type_init": {"type": "SEQ", "members": [{"type": "STRING", "value": ":="}, {"type": "SYMBOL", "name": "ref_value"}]}, "ref_type_decl": {"type": "SEQ", "members": [{"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "ref_spec_init"}]}, "ref_spec_init": {"type": "PREC_LEFT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "ref_spec"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": ":="}, {"type": "SYMBOL", "name": "ref_value"}]}, {"type": "BLANK"}]}]}}, "ref_spec": {"type": "SEQ", "members": [{"type": "STRING", "value": "REF_TO"}, {"type": "SYMBOL", "name": "_data_type_access"}]}, "ref_value": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "ref_addr"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "NULL"}, "named": true, "value": "null"}]}, "ref_addr": {"type": "SEQ", "members": [{"type": "STRING", "value": "REF"}, {"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "symbolic_variable"}, {"type": "SYMBOL", "name": "instance_name"}]}, {"type": "STRING", "value": ")"}]}, "ref_assign": {"type": "SEQ", "members": [{"type": "STRING", "value": ":="}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "ref_deref"}]}]}, "ref_deref": {"type": "PREC", "value": 9, "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "STRING", "value": "^"}]}}, "variable": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "direct_variable"}, {"type": "SYMBOL", "name": "symbolic_variable"}]}, "symbolic_variable": {"type": "SEQ", "members": [{"type": "FIELD", "name": "this", "content": {"type": "ALIAS", "content": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "THIS"}, {"type": "STRING", "value": "."}]}, {"type": "BLANK"}]}, "named": true, "value": "this"}}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "var_access"}, {"type": "SYMBOL", "name": "multi_elem_var"}]}]}, "var_access": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "ref_deref"}]}, "multi_elem_var": {"type": "SEQ", "members": [{"type": "FIELD", "name": "access", "content": {"type": "SYMBOL", "name": "var_access"}}, {"type": "PREC_LEFT", "value": 0, "content": {"type": "REPEAT1", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "subscript_list"}, {"type": "SYMBOL", "name": "struct_variable"}]}}}]}, "subscript_list": {"type": "SEQ", "members": [{"type": "STRING", "value": "["}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "_expression"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "_expression"}]}}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": "]"}]}, "struct_variable": {"type": "SEQ", "members": [{"type": "STRING", "value": "."}, {"type": "SYMBOL", "name": "var_access"}]}, "input_decls": {"type": "SEQ", "members": [{"type": "STRING", "value": "VAR_INPUT"}, {"type": "FIELD", "name": "retain", "content": {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "RETAIN"}, {"type": "STRING", "value": "NON_RETAIN"}]}, {"type": "BLANK"}]}}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "input_var"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}, {"type": "STRING", "value": "END_VAR"}]}, "input_var": {"type": "SEQ", "members": [{"type": "FIELD", "name": "variables", "content": {"type": "SYMBOL", "name": "variable_list"}}, {"type": "FIELD", "name": "type", "content": {"type": "SYMBOL", "name": "_input_var_kind"}}]}, "_input_var_kind": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "var_decl_init"}, {"type": "SYMBOL", "name": "edge_decl"}, {"type": "SYMBOL", "name": "array_conformand"}]}, "edge_decl": {"type": "SEQ", "members": [{"type": "FIELD", "name": "variables", "content": {"type": "SYMBOL", "name": "variable_list"}}, {"type": "STRING", "value": ":"}, {"type": "STRING", "value": "BOOL"}, {"type": "FIELD", "name": "edge", "content": {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "R_EDGE"}, {"type": "STRING", "value": "F_EDGE"}]}, {"type": "BLANK"}]}}]}, "var_decl_init": {"type": "SEQ", "members": [{"type": "FIELD", "name": "spec", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "simple_type_spec"}, {"type": "SYMBOL", "name": "str_type_spec"}, {"type": "SYMBOL", "name": "array_type_spec"}, {"type": "SYMBOL", "name": "struct_type_spec"}]}}, {"type": "FIELD", "name": "init", "content": {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "simple_type_init"}, {"type": "SYMBOL", "name": "array_type_init"}, {"type": "SYMBOL", "name": "struct_type_init"}]}, {"type": "BLANK"}]}}]}, "var_decl": {"type": "SEQ", "members": [{"type": "FIELD", "name": "spec", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "simple_type_spec"}, {"type": "SYMBOL", "name": "str_type_spec"}, {"type": "SYMBOL", "name": "array_type_spec"}, {"type": "SYMBOL", "name": "struct_type_spec"}]}}, {"type": "FIELD", "name": "init", "content": {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "simple_type_init"}, {"type": "SYMBOL", "name": "array_type_init"}, {"type": "SYMBOL", "name": "struct_type_init"}]}, {"type": "BLANK"}]}}]}, "variable_list": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "identifier"}]}}]}, "array_conformand": {"type": "SEQ", "members": [{"type": "STRING", "value": ":"}, {"type": "STRING", "value": "ARRAY"}, {"type": "STRING", "value": "["}, {"type": "SEQ", "members": [{"type": "STRING", "value": "*"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "STRING", "value": "*"}]}}]}, {"type": "STRING", "value": "]"}, {"type": "STRING", "value": "OF"}, {"type": "SYMBOL", "name": "_data_type_access"}]}, "fb_decl_no_init": {"type": "SEQ", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "fb_name"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "fb_name"}]}}]}, {"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "fq_name"}]}, "fb_decl_init": {"type": "SEQ", "members": [{"type": "STRING", "value": ":="}, {"type": "SYMBOL", "name": "struct_init"}]}, "fb_name": {"type": "SYMBOL", "name": "identifier"}, "output_decls": {"type": "SEQ", "members": [{"type": "STRING", "value": "VAR_OUTPUT"}, {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "RETAIN"}, {"type": "STRING", "value": "NON_RETAIN"}]}, {"type": "BLANK"}]}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "output_var"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}, {"type": "STRING", "value": "END_VAR"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}, "output_var": {"type": "SEQ", "members": [{"type": "FIELD", "name": "variables", "content": {"type": "SYMBOL", "name": "variable_list"}}, {"type": "FIELD", "name": "type", "content": {"type": "SYMBOL", "name": "_output_var_kind"}}]}, "_output_var_kind": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "var_decl_init"}, {"type": "SYMBOL", "name": "array_conformand"}]}, "in_out_decls": {"type": "SEQ", "members": [{"type": "STRING", "value": "VAR_IN_OUT"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "in_out_var"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}, {"type": "STRING", "value": "END_VAR"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}, "in_out_var": {"type": "SEQ", "members": [{"type": "FIELD", "name": "variables", "content": {"type": "SYMBOL", "name": "variable_list"}}, {"type": "FIELD", "name": "type", "content": {"type": "SYMBOL", "name": "_in_out_var_kind"}}]}, "_in_out_var_kind": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "var_decl"}, {"type": "SYMBOL", "name": "array_conformand"}, {"type": "SYMBOL", "name": "fb_decl_no_init"}]}, "var_decls": {"type": "SEQ", "members": [{"type": "STRING", "value": "VAR"}, {"type": "FIELD", "name": "constant", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "CONSTANT"}, {"type": "BLANK"}]}}, {"type": "FIELD", "name": "access", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "access_spec"}, {"type": "BLANK"}]}}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "var_decl_init_list"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}, {"type": "STRING", "value": "END_VAR"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}, "retain_var_decls": {"type": "SEQ", "members": [{"type": "STRING", "value": "VAR"}, {"type": "FIELD", "name": "retain", "content": {"type": "STRING", "value": "RETAIN"}}, {"type": "FIELD", "name": "access", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "access_spec"}, {"type": "BLANK"}]}}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "var_decl_init_list"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}, {"type": "STRING", "value": "END_VAR"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}, "var_decl_init_list": {"type": "SEQ", "members": [{"type": "FIELD", "name": "variables", "content": {"type": "SYMBOL", "name": "variable_list"}}, {"type": "FIELD", "name": "type", "content": {"type": "SYMBOL", "name": "var_decl"}}]}, "loc_var_decls": {"type": "SEQ", "members": [{"type": "STRING", "value": "VAR"}, {"type": "FIELD", "name": "constant_or_retain", "content": {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "CONSTANT"}, {"type": "STRING", "value": "RETAIN"}, {"type": "STRING", "value": "NON_RETAIN"}]}, {"type": "BLANK"}]}}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "loc_var_decl"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}, {"type": "STRING", "value": "END_VAR"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}, "loc_var_decl": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "FIELD", "name": "variable_name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "BLANK"}]}, {"type": "SYMBOL", "name": "located_at"}, {"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "loc_var_spec_init"}]}, "temp_var_decls": {"type": "SEQ", "members": [{"type": "STRING", "value": "VAR_TEMP"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "temp_var"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}, {"type": "STRING", "value": "END_VAR"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}, "temp_var": {"type": "SEQ", "members": [{"type": "FIELD", "name": "variables", "content": {"type": "SYMBOL", "name": "variable_list"}}, {"type": "FIELD", "name": "type", "content": {"type": "SYMBOL", "name": "_temp_var_kind"}}]}, "_temp_var_kind": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "var_decl"}, {"type": "SYMBOL", "name": "ref_spec"}]}, "external_var_decls": {"type": "SEQ", "members": [{"type": "STRING", "value": "VAR_EXTERNAL"}, {"type": "FIELD", "name": "constant", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "CONSTANT"}, {"type": "BLANK"}]}}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "external_decl"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}, {"type": "STRING", "value": "END_VAR"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}, "external_decl": {"type": "SEQ", "members": [{"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "FIELD", "name": "type", "content": {"type": "SYMBOL", "name": "_external_var_kind"}}]}, "_external_var_kind": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "var_decl"}, {"type": "SYMBOL", "name": "array_conformand"}]}, "global_var_decls": {"type": "SEQ", "members": [{"type": "STRING", "value": "VAR_GLOBAL"}, {"type": "FIELD", "name": "constant_or_retain", "content": {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "CONSTANT"}, {"type": "STRING", "value": "RETAIN"}]}, {"type": "BLANK"}]}}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "global_var_decl"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}, {"type": "STRING", "value": "END_VAR"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}, "global_var_decl": {"type": "SEQ", "members": [{"type": "FIELD", "name": "spec", "content": {"type": "SYMBOL", "name": "global_var_spec"}}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "type", "content": {"type": "SYMBOL", "name": "_global_var_kind"}}]}, "_global_var_kind": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "loc_var_spec_init"}, {"type": "SYMBOL", "name": "fq_name"}]}, "global_var_spec": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "identifier"}]}}]}]}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "located_at"}]}]}, "loc_var_spec_init": {"type": "SEQ", "members": [{"type": "FIELD", "name": "spec", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "simple_type_spec"}, {"type": "SYMBOL", "name": "array_type_spec"}, {"type": "SYMBOL", "name": "struct_type_spec"}, {"type": "SYMBOL", "name": "str_type_spec"}]}}, {"type": "FIELD", "name": "init", "content": {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "simple_type_init"}, {"type": "SYMBOL", "name": "array_type_init"}, {"type": "SYMBOL", "name": "struct_type_init"}]}, {"type": "BLANK"}]}}]}, "located_at": {"type": "SEQ", "members": [{"type": "STRING", "value": "AT"}, {"type": "SYMBOL", "name": "direct_variable"}]}, "loc_partly_var_decl": {"type": "SEQ", "members": [{"type": "STRING", "value": "VAR"}, {"type": "FIELD", "name": "retain", "content": {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "RETAIN"}, {"type": "STRING", "value": "NON_RETAIN"}]}, {"type": "BLANK"}]}}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "loc_partly_var"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}, {"type": "STRING", "value": "END_VAR"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}, "loc_partly_var": {"type": "SEQ", "members": [{"type": "FIELD", "name": "variable_name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "STRING", "value": "AT"}, {"type": "STRING", "value": "%"}, {"type": "SYMBOL", "name": "IQM"}, {"type": "STRING", "value": "*"}, {"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "var_spec"}]}, "var_spec": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "array_type_spec"}, {"type": "SYMBOL", "name": "fq_name"}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "STRING"}, {"type": "STRING", "value": "WSTRING"}]}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "["}, {"type": "SYMBOL", "name": "unsigned_int"}, {"type": "STRING", "value": "]"}]}, {"type": "BLANK"}]}]}]}, "func_decl": {"type": "SEQ", "members": [{"type": "STRING", "value": "FUNCTION"}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "FIELD", "name": "access", "content": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "_data_type_access"}]}, {"type": "BLANK"}]}}, {"type": "FIELD", "name": "directives", "content": {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "using_directive"}}}, {"type": "FIELD", "name": "variables", "content": {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "_func_variables"}}}, {"type": "FIELD", "name": "body", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "func_body"}, {"type": "BLANK"}]}}, {"type": "STRING", "value": "END_FUNCTION"}]}, "_func_variables": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "input_decls"}, {"type": "SYMBOL", "name": "output_decls"}, {"type": "SYMBOL", "name": "in_out_decls"}, {"type": "SYMBOL", "name": "external_var_decls"}, {"type": "SYMBOL", "name": "var_decls"}, {"type": "SYMBOL", "name": "temp_var_decls"}]}, "func_body": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "ladder_diagram"}, {"type": "SYMBOL", "name": "fb_diagram"}, {"type": "SYMBOL", "name": "stmt_list"}]}, "fb_decl": {"type": "SEQ", "members": [{"type": "STRING", "value": "FUNCTION_BLOCK"}, {"type": "FIELD", "name": "qualifier", "content": {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "FINAL"}, {"type": "STRING", "value": "ABSTRACT"}]}, {"type": "BLANK"}]}}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "FIELD", "name": "directives", "content": {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "using_directive"}}}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "EXTENDS"}, {"type": "FIELD", "name": "extends", "content": {"type": "SYMBOL", "name": "fq_name"}}]}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "IMPLEMENTS"}, {"type": "FIELD", "name": "implements", "content": {"type": "SYMBOL", "name": "interface_name_list"}}]}, {"type": "BLANK"}]}, {"type": "FIELD", "name": "variables", "content": {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "_fb_variables"}}}, {"type": "FIELD", "name": "method", "content": {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "method_decl"}}}, {"type": "FIELD", "name": "body", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "fb_body"}, {"type": "BLANK"}]}}, {"type": "STRING", "value": "END_FUNCTION_BLOCK"}]}, "_fb_variables": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "fb_input_decls"}, {"type": "SYMBOL", "name": "fb_output_decls"}, {"type": "SYMBOL", "name": "in_out_decls"}, {"type": "SYMBOL", "name": "temp_var_decls"}, {"type": "SYMBOL", "name": "external_var_decls"}, {"type": "SYMBOL", "name": "var_decls"}, {"type": "SYMBOL", "name": "retain_var_decls"}, {"type": "SYMBOL", "name": "no_retain_var_decls"}, {"type": "SYMBOL", "name": "loc_partly_var_decl"}]}, "fb_input_decls": {"type": "SEQ", "members": [{"type": "STRING", "value": "VAR_INPUT"}, {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "RETAIN"}, {"type": "STRING", "value": "NON_RETAIN"}]}, {"type": "BLANK"}]}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "fb_input_var"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}, {"type": "STRING", "value": "END_VAR"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}, "fb_input_var": {"type": "SEQ", "members": [{"type": "FIELD", "name": "variables", "content": {"type": "SYMBOL", "name": "variable_list"}}, {"type": "FIELD", "name": "type", "content": {"type": "SYMBOL", "name": "_fb_input_var_kind"}}]}, "_fb_input_var_kind": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "var_decl_init"}, {"type": "SYMBOL", "name": "edge_decl"}, {"type": "SYMBOL", "name": "array_conformand"}]}, "fb_output_decls": {"type": "SEQ", "members": [{"type": "STRING", "value": "VAR_OUTPUT"}, {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "RETAIN"}, {"type": "STRING", "value": "NON_RETAIN"}]}, {"type": "BLANK"}]}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "fb_output_var"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}, {"type": "STRING", "value": "END_VAR"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}, "fb_output_var": {"type": "SEQ", "members": [{"type": "FIELD", "name": "variables", "content": {"type": "SYMBOL", "name": "variable_list"}}, {"type": "FIELD", "name": "type", "content": {"type": "SYMBOL", "name": "_fb_output_var_kind"}}]}, "_fb_output_var_kind": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "var_decl_init"}, {"type": "SYMBOL", "name": "array_conformand"}]}, "no_retain_var_decls": {"type": "SEQ", "members": [{"type": "STRING", "value": "VAR"}, {"type": "STRING", "value": "NON_RETAIN"}, {"type": "FIELD", "name": "spec", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "access_spec"}, {"type": "BLANK"}]}}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "var_decl_init_list"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}, {"type": "STRING", "value": "END_VAR"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}, "fb_body": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "SFC"}, {"type": "SYMBOL", "name": "ladder_diagram"}, {"type": "SYMBOL", "name": "fb_diagram"}, {"type": "SYMBOL", "name": "stmt_list"}]}, "method_decl": {"type": "SEQ", "members": [{"type": "STRING", "value": "METHOD"}, {"type": "SYMBOL", "name": "access_spec"}, {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "FINAL"}, {"type": "STRING", "value": "ABSTRACT"}]}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": "OVERRIDE"}, {"type": "BLANK"}]}, {"type": "SYMBOL", "name": "identifier"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "_data_type_access"}]}, {"type": "BLANK"}]}, {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "input_decls"}, {"type": "SYMBOL", "name": "output_decls"}, {"type": "SYMBOL", "name": "in_out_decls"}, {"type": "SYMBOL", "name": "external_var_decls"}, {"type": "SYMBOL", "name": "var_decls"}, {"type": "SYMBOL", "name": "temp_var_decls"}]}}, {"type": "FIELD", "name": "body", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "func_body"}, {"type": "BLANK"}]}}, {"type": "STRING", "value": "END_METHOD"}]}, "class_decl": {"type": "SEQ", "members": [{"type": "STRING", "value": "CLASS"}, {"type": "FIELD", "name": "qualifier", "content": {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "FINAL"}, {"type": "STRING", "value": "ABSTRACT"}]}, {"type": "BLANK"}]}}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "class_type_name"}}, {"type": "FIELD", "name": "directives", "content": {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "using_directive"}}}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "EXTENDS"}, {"type": "FIELD", "name": "extends", "content": {"type": "SYMBOL", "name": "fq_name"}}]}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "IMPLEMENTS"}, {"type": "FIELD", "name": "implements", "content": {"type": "SYMBOL", "name": "interface_name_list"}}]}, {"type": "BLANK"}]}, {"type": "FIELD", "name": "declarations", "content": {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "_class_variables"}}}, {"type": "FIELD", "name": "methods", "content": {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "method_decl"}}}, {"type": "STRING", "value": "END_CLASS"}]}, "_class_variables": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "external_var_decls"}, {"type": "SYMBOL", "name": "var_decls"}, {"type": "SYMBOL", "name": "retain_var_decls"}, {"type": "SYMBOL", "name": "no_retain_var_decls"}, {"type": "SYMBOL", "name": "loc_partly_var_decl"}]}, "class_type_name": {"type": "SYMBOL", "name": "identifier"}, "instance_name": {"type": "PREC", "value": 9, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "REPEAT1", "content": {"type": "STRING", "value": "^"}}]}}, "interface_decl": {"type": "SEQ", "members": [{"type": "STRING", "value": "INTERFACE"}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "FIELD", "name": "directives", "content": {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "using_directive"}}}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "EXTENDS"}, {"type": "FIELD", "name": "extends", "content": {"type": "SYMBOL", "name": "interface_name_list"}}]}, {"type": "BLANK"}]}, {"type": "FIELD", "name": "prototype", "content": {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "method_prototype"}}}, {"type": "STRING", "value": "END_INTERFACE"}]}, "method_prototype": {"type": "SEQ", "members": [{"type": "STRING", "value": "METHOD"}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "data_type", "content": {"type": "SYMBOL", "name": "_data_type_access"}}]}, {"type": "BLANK"}]}, {"type": "FIELD", "name": "variables", "content": {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "input_decls"}, {"type": "SYMBOL", "name": "output_decls"}, {"type": "SYMBOL", "name": "in_out_decls"}]}}}, {"type": "STRING", "value": "END_METHOD"}]}, "interface_spec_init": {"type": "SEQ", "members": [{"type": "STRING", "value": ":="}, {"type": "SYMBOL", "name": "interface_value"}]}, "interface_value": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "symbolic_variable"}, {"type": "SYMBOL", "name": "instance_name"}, {"type": "STRING", "value": "NULL"}]}, "interface_name_list": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "fq_name"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "fq_name"}]}}]}, "interface_name": {"type": "SYMBOL", "name": "identifier"}, "access_spec": {"type": "CHOICE", "members": [{"type": "STRING", "value": "PUBLIC"}, {"type": "STRING", "value": "PROTECTED"}, {"type": "STRING", "value": "PRIVATE"}, {"type": "STRING", "value": "INTERNAL"}]}, "prog_decl": {"type": "SEQ", "members": [{"type": "STRING", "value": "PROGRAM"}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "FIELD", "name": "declarations", "content": {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "input_decls"}, {"type": "SYMBOL", "name": "output_decls"}, {"type": "SYMBOL", "name": "in_out_decls"}, {"type": "SYMBOL", "name": "external_var_decls"}, {"type": "SYMBOL", "name": "var_decls"}, {"type": "SYMBOL", "name": "temp_var_decls"}, {"type": "SYMBOL", "name": "retain_var_decls"}, {"type": "SYMBOL", "name": "no_retain_var_decls"}, {"type": "SYMBOL", "name": "loc_partly_var_decl"}, {"type": "SYMBOL", "name": "loc_var_decls"}, {"type": "SYMBOL", "name": "prog_access_decl"}]}}}, {"type": "FIELD", "name": "body", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "fb_body"}, {"type": "BLANK"}]}}, {"type": "STRING", "value": "END_PROGRAM"}]}, "prog_type_access": {"type": "SYMBOL", "name": "fq_name"}, "prog_access_decls": {"type": "SEQ", "members": [{"type": "STRING", "value": "ref_deref"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "prog_access_decl"}, {"type": "STRING", "value": ";"}]}}, {"type": "SYMBOL", "name": "identifier"}]}, "prog_access_decl": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "access_name"}, {"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "symbolic_variable"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "multibit_part_access"}, {"type": "BLANK"}]}, {"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "_data_type_access"}, {"type": "SYMBOL", "name": "access_direction"}]}, "SFC": {"type": "REPEAT1", "content": {"type": "SYMBOL", "name": "SFC_network"}}, "SFC_network": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "initial_step"}, {"type": "REPEAT", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "step"}, {"type": "SYMBOL", "name": "transition"}, {"type": "SYMBOL", "name": "action"}]}}]}, "initial_step": {"type": "SEQ", "members": [{"type": "STRING", "value": "INITIAL_STEP"}, {"type": "SYMBOL", "name": "step_name"}, {"type": "STRING", "value": ":"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "action_association"}, {"type": "STRING", "value": ";"}]}}, {"type": "STRING", "value": "END_STEP"}]}, "step": {"type": "SEQ", "members": [{"type": "STRING", "value": "STEP"}, {"type": "SYMBOL", "name": "step_name"}, {"type": "STRING", "value": ":"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "action_association"}, {"type": "STRING", "value": ";"}]}}, {"type": "STRING", "value": "END_STEP"}]}, "step_name": {"type": "SYMBOL", "name": "identifier"}, "action_association": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "action_name"}, {"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "action_qualifier"}, {"type": "BLANK"}]}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "variable_name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "STRING", "value": ";"}]}}, {"type": "STRING", "value": ")"}]}, "action_name": {"type": "SYMBOL", "name": "identifier"}, "action_qualifier": {"type": "CHOICE", "members": [{"type": "STRING", "value": "N"}, {"type": "STRING", "value": "R"}, {"type": "STRING", "value": "S"}, {"type": "STRING", "value": "P"}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "L"}, {"type": "STRING", "value": "D"}, {"type": "STRING", "value": "SD"}, {"type": "STRING", "value": "DS"}, {"type": "STRING", "value": "SL"}]}, {"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "action_time"}]}]}, "action_time": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "duration"}, {"type": "FIELD", "name": "variable_name", "content": {"type": "SYMBOL", "name": "identifier"}}]}, "transition": {"type": "SEQ", "members": [{"type": "STRING", "value": "TRANSITION"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "transition_name"}, {"type": "BLANK"}]}, {"type": "STRING", "value": ":"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "STRING", "value": "PRIORITY"}, {"type": "STRING", "value": ":="}, {"type": "SYMBOL", "name": "unsigned_int"}, {"type": "STRING", "value": ")"}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": "FROM"}, {"type": "SYMBOL", "name": "steps"}, {"type": "STRING", "value": "TO"}, {"type": "SYMBOL", "name": "steps"}, {"type": "SYMBOL", "name": "transition_cond"}, {"type": "STRING", "value": "END_TRANSITION"}]}, "transition_name": {"type": "SYMBOL", "name": "identifier"}, "steps": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "step_name"}, {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "SYMBOL", "name": "step_name"}, {"type": "REPEAT1", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "step_name"}]}}, {"type": "STRING", "value": ")"}]}]}, "transition_cond": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": ":="}, {"type": "SYMBOL", "name": "_expression"}, {"type": "STRING", "value": ";"}]}, {"type": "SEQ", "members": [{"type": "STRING", "value": ":"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "fbd_network"}, {"type": "SYMBOL", "name": "ld_rung"}]}]}]}, "action": {"type": "SEQ", "members": [{"type": "STRING", "value": "ACTION"}, {"type": "SYMBOL", "name": "action_name"}, {"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "fb_body"}, {"type": "STRING", "value": "END_ACTION"}]}, "config_name": {"type": "SYMBOL", "name": "identifier"}, "resource_type_name": {"type": "SYMBOL", "name": "identifier"}, "config_decl": {"type": "SEQ", "members": [{"type": "STRING", "value": "CONFIGURATION"}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "config_name"}}, {"type": "FIELD", "name": "global_variables", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "global_var_decls"}, {"type": "BLANK"}]}}, {"type": "FIELD", "name": "ressources", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "single_resource_decl"}, {"type": "REPEAT1", "content": {"type": "SYMBOL", "name": "resource_decl"}}]}}, {"type": "STRING", "value": "END_CONFIGURATION"}]}, "resource_decl": {"type": "SEQ", "members": [{"type": "STRING", "value": "RESOURCE"}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "STRING", "value": "ON"}, {"type": "FIELD", "name": "resource_type_name", "content": {"type": "SYMBOL", "name": "resource_type_name"}}, {"type": "FIELD", "name": "global_variables", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "global_var_decls"}, {"type": "BLANK"}]}}, {"type": "FIELD", "name": "ressource", "content": {"type": "SYMBOL", "name": "single_resource_decl"}}, {"type": "STRING", "value": "END_RESOURCE"}]}, "single_resource_decl": {"type": "SEQ", "members": [{"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "task_config"}, {"type": "STRING", "value": ";"}]}}, {"type": "REPEAT1", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "prog_config"}, {"type": "STRING", "value": ";"}]}}]}, "access_decls": {"type": "SEQ", "members": [{"type": "STRING", "value": "ref_deref"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "access_decl"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}, {"type": "STRING", "value": "END_VAR"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}, "access_decl": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "access_name"}, {"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "access_path"}, {"type": "STRING", "value": ":"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "_data_type_access"}, {"type": "SYMBOL", "name": "access_direction"}]}, {"type": "BLANK"}]}]}, "access_path": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "STRING", "value": "."}]}, {"type": "BLANK"}]}, {"type": "SYMBOL", "name": "direct_variable"}]}, {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "STRING", "value": "."}]}, {"type": "BLANK"}]}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "instance_name"}]}, {"type": "STRING", "value": "."}]}}, {"type": "SYMBOL", "name": "symbolic_variable"}]}]}, "global_ref_deref": {"type": "PREC_LEFT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "SEQ", "members": [{"type": "FIELD", "name": "ressource", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "STRING", "value": "."}]}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "."}, {"type": "FIELD", "name": "struct", "content": {"type": "SYMBOL", "name": "identifier"}}]}, {"type": "BLANK"}]}]}}, "access_name": {"type": "SYMBOL", "name": "identifier"}, "prog_output_access": {"type": "SEQ", "members": [{"type": "FIELD", "name": "prog_name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "STRING", "value": "."}, {"type": "SYMBOL", "name": "symbolic_variable"}]}, "access_direction": {"type": "CHOICE", "members": [{"type": "STRING", "value": "READ_WRITE"}, {"type": "STRING", "value": "READ_ONLY"}]}, "task_config": {"type": "SEQ", "members": [{"type": "STRING", "value": "TASK"}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "FIELD", "name": "init", "content": {"type": "SYMBOL", "name": "task_init"}}]}, "task_init": {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "SINGLE"}, {"type": "STRING", "value": ":="}, {"type": "FIELD", "name": "single", "content": {"type": "SYMBOL", "name": "data_source"}}, {"type": "STRING", "value": ","}]}, {"type": "BLANK"}]}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "INTERVAL"}, {"type": "STRING", "value": ":="}, {"type": "FIELD", "name": "interval", "content": {"type": "SYMBOL", "name": "data_source"}}, {"type": "STRING", "value": ","}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": "PRIORITY"}, {"type": "STRING", "value": ":="}, {"type": "FIELD", "name": "priority", "content": {"type": "SYMBOL", "name": "unsigned_int"}}, {"type": "STRING", "value": ")"}]}, "data_source": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "constant"}, {"type": "SYMBOL", "name": "global_ref_deref"}, {"type": "SYMBOL", "name": "prog_output_access"}, {"type": "SYMBOL", "name": "direct_variable"}]}, "prog_config": {"type": "SEQ", "members": [{"type": "STRING", "value": "PROGRAM"}, {"type": "FIELD", "name": "retain", "content": {"type": "CHOICE", "members": [{"type": "CHOICE", "members": [{"type": "STRING", "value": "RETAIN"}, {"type": "STRING", "value": "NON_RETAIN"}]}, {"type": "BLANK"}]}}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "FIELD", "name": "task", "content": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "WITH"}, {"type": "SYMBOL", "name": "identifier"}]}, {"type": "BLANK"}]}}, {"type": "STRING", "value": ":"}, {"type": "FIELD", "name": "access", "content": {"type": "SYMBOL", "name": "prog_type_access"}}, {"type": "FIELD", "name": "configuration_elements", "content": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "SYMBOL", "name": "prog_conf_elems"}, {"type": "STRING", "value": ")"}]}, {"type": "BLANK"}]}}]}, "prog_conf_elems": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "prog_conf_elem"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "prog_conf_elem"}]}}]}, "prog_conf_elem": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "fb_task"}, {"type": "SYMBOL", "name": "prog_cnxn"}]}, "fb_task": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "instance_name"}, {"type": "STRING", "value": "WITH"}, {"type": "FIELD", "name": "task", "content": {"type": "SYMBOL", "name": "identifier"}}]}, "prog_cnxn": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "symbolic_variable"}, {"type": "STRING", "value": ":="}, {"type": "SYMBOL", "name": "prog_data_source"}]}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "symbolic_variable"}, {"type": "STRING", "value": "=>"}, {"type": "SYMBOL", "name": "data_sink"}]}]}, "prog_data_source": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "constant"}, {"type": "SYMBOL", "name": "enum_value"}, {"type": "SYMBOL", "name": "global_ref_deref"}, {"type": "SYMBOL", "name": "direct_variable"}]}, "data_sink": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "global_ref_deref"}, {"type": "SYMBOL", "name": "direct_variable"}]}, "config_init": {"type": "SEQ", "members": [{"type": "STRING", "value": "VAR_CONFIG"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "config_inst_init"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}, {"type": "STRING", "value": "END_VAR"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}, "config_inst_init": {"type": "SEQ", "members": [{"type": "FIELD", "name": "resource", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "STRING", "value": "."}, {"type": "FIELD", "name": "prog", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "STRING", "value": "."}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "instance_name"}]}, {"type": "STRING", "value": "."}]}}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "located_at"}, {"type": "BLANK"}]}, {"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "loc_var_spec_init"}]}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "instance_name"}, {"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "fq_name"}, {"type": "STRING", "value": ":="}, {"type": "SYMBOL", "name": "struct_init"}]}]}]}, "namespace_decl": {"type": "SEQ", "members": [{"type": "STRING", "value": "NAMESPACE"}, {"type": "FIELD", "name": "internal", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "INTERNAL"}, {"type": "BLANK"}]}}, {"type": "FIELD", "name": "name", "content": {"type": "SYMBOL", "name": "namespace_h_name"}}, {"type": "FIELD", "name": "directives", "content": {"type": "REPEAT", "content": {"type": "SYMBOL", "name": "using_directive"}}}, {"type": "FIELD", "name": "elements", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "namespace_elements"}, {"type": "BLANK"}]}}, {"type": "STRING", "value": "END_NAMESPACE"}]}, "namespace_elements": {"type": "REPEAT1", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "data_type_decl"}, {"type": "SYMBOL", "name": "func_decl"}, {"type": "SYMBOL", "name": "fb_decl"}, {"type": "SYMBOL", "name": "class_decl"}, {"type": "SYMBOL", "name": "interface_decl"}, {"type": "SYMBOL", "name": "namespace_decl"}]}}, "namespace_h_name": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "."}, {"type": "SYMBOL", "name": "identifier"}]}}]}, "using_directive": {"type": "SEQ", "members": [{"type": "STRING", "value": "USING"}, {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "namespace_h_name"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "namespace_h_name"}]}}]}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}, "pou_decl": {"type": "SEQ", "members": [{"type": "REPEAT", "content": {"type": "SYMBOL", "name": "using_directive"}}, {"type": "REPEAT1", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "global_var_decls"}, {"type": "SYMBOL", "name": "data_type_decl"}, {"type": "SYMBOL", "name": "access_decls"}, {"type": "SYMBOL", "name": "func_decl"}, {"type": "SYMBOL", "name": "fb_decl"}, {"type": "SYMBOL", "name": "class_decl"}, {"type": "SYMBOL", "name": "interface_decl"}, {"type": "SYMBOL", "name": "namespace_decl"}]}}]}, "_expression": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "boolean_operator"}, {"type": "SYMBOL", "name": "comparison_operator"}, {"type": "SYMBOL", "name": "add_operator"}, {"type": "SYMBOL", "name": "mult_operator"}, {"type": "SYMBOL", "name": "power_operator"}, {"type": "SYMBOL", "name": "unary_operator"}, {"type": "SYMBOL", "name": "_primary_expression"}]}, "_primary_expression": {"type": "PREC_LEFT", "value": 0, "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "constant"}, {"type": "SYMBOL", "name": "fq_name"}, {"type": "SYMBOL", "name": "enum_value"}, {"type": "SYMBOL", "name": "variable_access"}, {"type": "SYMBOL", "name": "func_call"}, {"type": "SYMBOL", "name": "ref_value"}, {"type": "SYMBOL", "name": "parenthesized_expression"}]}}, "parenthesized_expression": {"type": "SEQ", "members": [{"type": "STRING", "value": "("}, {"type": "SYMBOL", "name": "_expression"}, {"type": "STRING", "value": ")"}]}, "boolean_operator": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "or_operator"}, {"type": "SYMBOL", "name": "xor_operator"}, {"type": "SYMBOL", "name": "and_operator"}]}, "or_operator": {"type": "PREC_LEFT", "value": 1, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "_expression"}}, {"type": "STRING", "value": "OR"}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "_expression"}}]}}, "xor_operator": {"type": "PREC_LEFT", "value": 2, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "_expression"}}, {"type": "STRING", "value": "XOR"}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "_expression"}}]}}, "and_operator": {"type": "PREC_LEFT", "value": 3, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "_expression"}}, {"type": "CHOICE", "members": [{"type": "STRING", "value": "&"}, {"type": "STRING", "value": "AND"}]}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "_expression"}}]}}, "comparison_operator": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "eq_operator"}, {"type": "SYMBOL", "name": "ord_operator"}]}, "eq_operator": {"type": "PREC_LEFT", "value": 4, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "SYMBOL", "name": "eq"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "_expression"}}]}}, "ord_operator": {"type": "PREC_LEFT", "value": 4, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "SYMBOL", "name": "ord"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "_expression"}}]}}, "eq": {"type": "PREC", "value": 4, "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "="}, {"type": "STRING", "value": "<>"}]}}, "ord": {"type": "PREC", "value": 4, "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "<"}, {"type": "STRING", "value": ">"}, {"type": "STRING", "value": "<="}, {"type": "STRING", "value": ">="}]}}, "add_operator": {"type": "PREC_LEFT", "value": 5, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "SYMBOL", "name": "add"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "_expression"}}]}}, "add": {"type": "PREC", "value": 5, "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "+"}, {"type": "STRING", "value": "-"}]}}, "mult_operator": {"type": "PREC_LEFT", "value": 6, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "_expression"}}, {"type": "FIELD", "name": "operator", "content": {"type": "SYMBOL", "name": "mult"}}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "_expression"}}]}}, "mult": {"type": "PREC", "value": 6, "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "*"}, {"type": "STRING", "value": "/"}, {"type": "STRING", "value": "MOD"}]}}, "power_operator": {"type": "PREC_LEFT", "value": 7, "content": {"type": "SEQ", "members": [{"type": "FIELD", "name": "left", "content": {"type": "SYMBOL", "name": "_expression"}}, {"type": "STRING", "value": "**"}, {"type": "FIELD", "name": "right", "content": {"type": "SYMBOL", "name": "_expression"}}]}}, "unary_operator": {"type": "SEQ", "members": [{"type": "FIELD", "name": "operator", "content": {"type": "SYMBOL", "name": "unary"}}, {"type": "FIELD", "name": "expr", "content": {"type": "SYMBOL", "name": "_expression"}}]}, "unary": {"type": "PREC", "value": 8, "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "-"}, {"type": "STRING", "value": "+"}, {"type": "STRING", "value": "NOT"}]}}, "constant_expr": {"type": "SYMBOL", "name": "_expression"}, "variable_access": {"type": "SEQ", "members": [{"type": "FIELD", "name": "variable", "content": {"type": "SYMBOL", "name": "variable"}}, {"type": "FIELD", "name": "access", "content": {"type": "SYMBOL", "name": "multibit_part_access"}}]}, "multibit_part_access": {"type": "SEQ", "members": [{"type": "STRING", "value": "."}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "unsigned_int"}, {"type": "SEQ", "members": [{"type": "STRING", "value": "%"}, {"type": "FIELD", "name": "size", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "XBWDL"}, {"type": "BLANK"}]}}, {"type": "SYMBOL", "name": "unsigned_int"}]}]}]}, "func_call": {"type": "SEQ", "members": [{"type": "FIELD", "name": "target", "content": {"type": "SYMBOL", "name": "fq_name"}}, {"type": "STRING", "value": "("}, {"type": "PREC", "value": 10, "content": {"type": "FIELD", "name": "params", "content": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "param_assign"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "param_assign"}]}}]}, {"type": "BLANK"}]}}}, {"type": "STRING", "value": ")"}]}, "stmt_list": {"type": "PREC_LEFT", "value": 0, "content": {"type": "REPEAT1", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "stmt"}, {"type": "CHOICE", "members": [{"type": "STRING", "value": ";"}, {"type": "BLANK"}]}]}}}, "stmt": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "assign"}, {"type": "SYMBOL", "name": "func_call"}, {"type": "SYMBOL", "name": "invocation"}, {"type": "ALIAS", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "SUPER"}, {"type": "STRING", "value": "("}, {"type": "STRING", "value": ")"}]}, "named": true, "value": "super"}, {"type": "STRING", "value": "RETURN"}, {"type": "SYMBOL", "name": "if_stmt"}, {"type": "SYMBOL", "name": "case_stmt"}, {"type": "SYMBOL", "name": "for_stmt"}, {"type": "SYMBOL", "name": "while_stmt"}, {"type": "SYMBOL", "name": "repeat_stmt"}, {"type": "STRING", "value": "EXIT"}, {"type": "STRING", "value": "CONTINUE"}]}, "assign": {"type": "SEQ", "members": [{"type": "FIELD", "name": "variable", "content": {"type": "SYMBOL", "name": "variable"}}, {"type": "FIELD", "name": "target", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "assignment_attempt"}, {"type": "SYMBOL", "name": "assignment"}, {"type": "SYMBOL", "name": "ref_assign"}]}}]}, "assignment": {"type": "SEQ", "members": [{"type": "STRING", "value": ":="}, {"type": "SYMBOL", "name": "_expression"}]}, "assignment_attempt": {"type": "SEQ", "members": [{"type": "STRING", "value": "?="}, {"type": "FIELD", "name": "target", "content": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "identifier"}, {"type": "SYMBOL", "name": "ref_deref"}, {"type": "SYMBOL", "name": "ref_value"}]}}]}, "invocation": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "instance_name"}, {"type": "ALIAS", "content": {"type": "STRING", "value": "THIS"}, "named": true, "value": "this"}, {"type": "SYMBOL", "name": "this_invocation"}]}, {"type": "STRING", "value": "("}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "param_assign"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "param_assign"}]}}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": ")"}]}, "this_invocation": {"type": "SEQ", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "THIS"}, {"type": "STRING", "value": "."}]}, {"type": "REPEAT1", "content": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "instance_name"}, {"type": "STRING", "value": "."}]}}, {"type": "ALIAS", "content": {"type": "SYMBOL", "name": "identifier"}, "named": true, "value": "method_name"}, {"type": "STRING", "value": "("}, {"type": "PREC", "value": 10, "content": {"type": "FIELD", "name": "params", "content": {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "SYMBOL", "name": "param_assign"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "param_assign"}]}}]}, {"type": "BLANK"}]}}}, {"type": "STRING", "value": ")"}]}, "param_assign": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "param_assign_input"}, {"type": "SYMBOL", "name": "param_assign_output"}]}, "param_assign_input": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "FIELD", "name": "param", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "STRING", "value": ":="}]}, {"type": "BLANK"}]}, {"type": "FIELD", "name": "value", "content": {"type": "SYMBOL", "name": "_expression"}}]}, "param_assign_output": {"type": "SEQ", "members": [{"type": "FIELD", "name": "not", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "NOT"}, {"type": "BLANK"}]}}, {"type": "FIELD", "name": "param", "content": {"type": "SYMBOL", "name": "identifier"}}, {"type": "STRING", "value": "=>"}, {"type": "FIELD", "name": "variable", "content": {"type": "SYMBOL", "name": "variable"}}]}, "if_stmt": {"type": "SEQ", "members": [{"type": "STRING", "value": "IF"}, {"type": "SYMBOL", "name": "_expression"}, {"type": "STRING", "value": "THEN"}, {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "stmt_list"}, {"type": "BLANK"}]}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "ELSE IF"}, {"type": "SYMBOL", "name": "_expression"}, {"type": "STRING", "value": "THEN"}, {"type": "SYMBOL", "name": "stmt_list"}]}}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "ELSE"}, {"type": "SYMBOL", "name": "stmt_list"}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": "END_IF"}]}, "case_stmt": {"type": "SEQ", "members": [{"type": "STRING", "value": "CASE"}, {"type": "SYMBOL", "name": "_expression"}, {"type": "STRING", "value": "OF"}, {"type": "REPEAT1", "content": {"type": "SYMBOL", "name": "case_selection"}}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "ELSE"}, {"type": "SYMBOL", "name": "stmt_list"}]}, {"type": "BLANK"}]}, {"type": "STRING", "value": "END_CASE"}]}, "case_selection": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "case_list"}, {"type": "STRING", "value": ":"}, {"type": "SYMBOL", "name": "stmt_list"}]}, "case_list": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "case_list_elem"}, {"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": ","}, {"type": "SYMBOL", "name": "case_list_elem"}]}}]}, "case_list_elem": {"type": "CHOICE", "members": [{"type": "SYMBOL", "name": "subrange"}, {"type": "SYMBOL", "name": "constant_expr"}]}, "for_stmt": {"type": "SEQ", "members": [{"type": "STRING", "value": "FOR"}, {"type": "SYMBOL", "name": "control_variable"}, {"type": "STRING", "value": ":="}, {"type": "SYMBOL", "name": "for_list"}, {"type": "STRING", "value": "DO"}, {"type": "SYMBOL", "name": "stmt_list"}, {"type": "STRING", "value": "END_FOR"}]}, "control_variable": {"type": "SYMBOL", "name": "identifier"}, "for_list": {"type": "SEQ", "members": [{"type": "SYMBOL", "name": "_expression"}, {"type": "STRING", "value": "TO"}, {"type": "SYMBOL", "name": "_expression"}, {"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "STRING", "value": "BY"}, {"type": "SYMBOL", "name": "_expression"}]}, {"type": "BLANK"}]}]}, "while_stmt": {"type": "SEQ", "members": [{"type": "STRING", "value": "WHILE"}, {"type": "SYMBOL", "name": "_primary_expression"}, {"type": "STRING", "value": "DO"}, {"type": "SYMBOL", "name": "stmt_list"}, {"type": "STRING", "value": "END_WHILE"}]}, "repeat_stmt": {"type": "SEQ", "members": [{"type": "STRING", "value": "REPEAT"}, {"type": "SYMBOL", "name": "stmt_list"}, {"type": "STRING", "value": "UNTIL"}, {"type": "SYMBOL", "name": "_expression"}, {"type": "STRING", "value": "END_REPEAT"}]}, "fq_name": {"type": "PREC_LEFT", "value": 0, "content": {"type": "SEQ", "members": [{"type": "CHOICE", "members": [{"type": "SEQ", "members": [{"type": "REPEAT", "content": {"type": "SEQ", "members": [{"type": "STRING", "value": "::"}, {"type": "FIELD", "name": "fragment", "content": {"type": "SYMBOL", "name": "identifier"}}]}}, {"type": "STRING", "value": "::"}]}, {"type": "BLANK"}]}, {"type": "FIELD", "name": "target", "content": {"type": "SYMBOL", "name": "identifier"}}]}}, "IQM": {"type": "CHOICE", "members": [{"type": "STRING", "value": "I"}, {"type": "STRING", "value": "Q"}, {"type": "STRING", "value": "M"}]}, "XBWDL": {"type": "CHOICE", "members": [{"type": "STRING", "value": "X"}, {"type": "STRING", "value": "B"}, {"type": "STRING", "value": "W"}, {"type": "STRING", "value": "D"}, {"type": "STRING", "value": "L"}]}, "ladder_diagram": {"type": "REPEAT1", "content": {"type": "SYMBOL", "name": "ld_rung"}}, "ld_rung": {"type": "STRING", "value": "todo_lad"}, "fb_diagram": {"type": "REPEAT1", "content": {"type": "SYMBOL", "name": "fbd_network"}}, "fbd_network": {"type": "STRING", "value": "todo_fbd"}, "_bit": {"type": "PATTERN", "value": "[01]"}, "_octal_digit": {"type": "PATTERN", "value": "[0-7]"}, "_hex_digit": {"type": "PATTERN", "value": "[0-9a-fA-F]"}, "identifier": {"type": "PATTERN", "value": "[_\\p{XID_Start}][_\\p{XID_Continue}]*"}}, "extras": [{"type": "PATTERN", "value": "\\s"}, {"type": "SYMBOL", "name": "comment"}, {"type": "TOKEN", "content": {"type": "CHOICE", "members": [{"type": "STRING", "value": "\t"}, {"type": "STRING", "value": "\r"}, {"type": "STRING", "value": "\n"}]}}], "conflicts": [["var_decls", "loc_var_decls"], ["var_decls", "loc_var_decls", "loc_partly_var_decl"], ["var_decls", "loc_partly_var_decl"], ["retain_var_decls", "loc_var_decls", "loc_partly_var_decl"], ["retain_var_decls", "loc_partly_var_decl"], ["no_retain_var_decls", "loc_var_decls", "loc_partly_var_decl"], ["no_retain_var_decls", "loc_partly_var_decl"], ["numeric_literal", "enum_value_spec"], ["subrange_type_spec", "numeric_type_name"], ["symbolic_variable", "multi_elem_var"], ["fq_name"], ["global_ref_deref", "var_access"]], "precedences": [], "externals": [], "inline": [], "supertypes": ["_func_variables", "_fb_variables", "_class_variables", "_input_var_kind", "_fb_input_var_kind", "_output_var_kind", "_fb_output_var_kind", "_temp_var_kind", "_in_out_var_kind", "_external_var_kind", "_global_var_kind", "_data_type_access", "_elem_type_name", "_expression", "_primary_expression", "unary", "add", "mult", "eq", "ord", "IQM", "XBWDL"], "reserved": {"global": [{"type": "STRING", "value": "PROGRAM"}, {"type": "STRING", "value": "END_PROGRAM"}, {"type": "STRING", "value": "CONFIGURATION"}, {"type": "STRING", "value": "END_CONFIGURATION"}, {"type": "STRING", "value": "RESOURCE"}, {"type": "STRING", "value": "END_RESOURCE"}, {"type": "STRING", "value": "NAMESPACE"}, {"type": "STRING", "value": "END_NAMESPACE"}, {"type": "STRING", "value": "USING"}, {"type": "STRING", "value": "CLASS"}, {"type": "STRING", "value": "END_CLASS"}, {"type": "STRING", "value": "INTERFACE"}, {"type": "STRING", "value": "END_INTERFACE"}, {"type": "STRING", "value": "METHOD"}, {"type": "STRING", "value": "END_METHOD"}, {"type": "STRING", "value": "FUNCTION"}, {"type": "STRING", "value": "END_FUNCTION"}, {"type": "STRING", "value": "FUNCTION_BLOCK"}, {"type": "STRING", "value": "END_FUNCTION_BLOCK"}, {"type": "STRING", "value": "TYPE"}, {"type": "STRING", "value": "END_TYPE"}, {"type": "STRING", "value": "VAR"}, {"type": "STRING", "value": "END_VAR"}, {"type": "STRING", "value": "VAR_INPUT"}, {"type": "STRING", "value": "VAR_OUTPUT"}, {"type": "STRING", "value": "VAR_IN_OUT"}, {"type": "STRING", "value": "VAR_TEMP"}, {"type": "STRING", "value": "VAR_EXTERNAL"}, {"type": "STRING", "value": "VAR_GLOBAL"}, {"type": "STRING", "value": "IF"}, {"type": "STRING", "value": "THEN"}, {"type": "STRING", "value": "ELSE"}, {"type": "STRING", "value": "END_IF"}, {"type": "STRING", "value": "CASE"}, {"type": "STRING", "value": "OF"}, {"type": "STRING", "value": "END_CASE"}, {"type": "STRING", "value": "FOR"}, {"type": "STRING", "value": "TO"}, {"type": "STRING", "value": "BY"}, {"type": "STRING", "value": "END_FOR"}, {"type": "STRING", "value": "REPEAT"}, {"type": "STRING", "value": "UNTIL"}, {"type": "STRING", "value": "END_REPEAT"}, {"type": "STRING", "value": "WHILE"}, {"type": "STRING", "value": "END_WHILE"}, {"type": "STRING", "value": "DO"}, {"type": "STRING", "value": "EXIT"}, {"type": "STRING", "value": "RETURN"}, {"type": "STRING", "value": "BOOL"}, {"type": "STRING", "value": "BYTE"}, {"type": "STRING", "value": "WORD"}, {"type": "STRING", "value": "DWORD"}, {"type": "STRING", "value": "LWORD"}, {"type": "STRING", "value": "SINT"}, {"type": "STRING", "value": "INT"}, {"type": "STRING", "value": "DINT"}, {"type": "STRING", "value": "LINT"}, {"type": "STRING", "value": "USINT"}, {"type": "STRING", "value": "UINT"}, {"type": "STRING", "value": "UDINT"}, {"type": "STRING", "value": "ULINT"}, {"type": "STRING", "value": "REAL"}, {"type": "STRING", "value": "LREAL"}, {"type": "STRING", "value": "CHAR"}, {"type": "STRING", "value": "WCHAR"}, {"type": "STRING", "value": "STRING"}, {"type": "STRING", "value": "WSTRING"}, {"type": "STRING", "value": "DATE"}, {"type": "STRING", "value": "TIME"}, {"type": "STRING", "value": "DT"}, {"type": "STRING", "value": "TOD"}, {"type": "STRING", "value": "LDATE"}, {"type": "STRING", "value": "LTIME"}, {"type": "STRING", "value": "LDT"}, {"type": "STRING", "value": "LTOD"}]}}