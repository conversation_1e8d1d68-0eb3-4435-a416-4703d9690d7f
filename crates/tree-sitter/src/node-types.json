[{"type": "IQM", "named": true, "subtypes": [{"type": "I", "named": false}, {"type": "M", "named": false}, {"type": "Q", "named": false}]}, {"type": "XBWDL", "named": true, "subtypes": [{"type": "B", "named": false}, {"type": "D", "named": false}, {"type": "L", "named": false}, {"type": "W", "named": false}, {"type": "X", "named": false}]}, {"type": "_class_variables", "named": true, "subtypes": [{"type": "external_var_decls", "named": true}, {"type": "loc_partly_var_decl", "named": true}, {"type": "no_retain_var_decls", "named": true}, {"type": "retain_var_decls", "named": true}, {"type": "var_decls", "named": true}]}, {"type": "_data_type_access", "named": true, "subtypes": [{"type": "_elem_type_name", "named": true}, {"type": "fq_name", "named": true}]}, {"type": "_elem_type_name", "named": true, "subtypes": [{"type": "bit_str_type_name", "named": true}, {"type": "date_type_name", "named": true}, {"type": "dt_type_name", "named": true}, {"type": "numeric_type_name", "named": true}, {"type": "time_type_name", "named": true}, {"type": "tod_type_name", "named": true}]}, {"type": "_expression", "named": true, "subtypes": [{"type": "_primary_expression", "named": true}, {"type": "add_operator", "named": true}, {"type": "boolean_operator", "named": true}, {"type": "comparison_operator", "named": true}, {"type": "mult_operator", "named": true}, {"type": "power_operator", "named": true}, {"type": "unary_operator", "named": true}]}, {"type": "_external_var_kind", "named": true, "subtypes": [{"type": "array_conformand", "named": true}, {"type": "var_decl", "named": true}]}, {"type": "_fb_input_var_kind", "named": true, "subtypes": [{"type": "array_conformand", "named": true}, {"type": "edge_decl", "named": true}, {"type": "var_decl_init", "named": true}]}, {"type": "_fb_output_var_kind", "named": true, "subtypes": [{"type": "array_conformand", "named": true}, {"type": "var_decl_init", "named": true}]}, {"type": "_fb_variables", "named": true, "subtypes": [{"type": "external_var_decls", "named": true}, {"type": "fb_input_decls", "named": true}, {"type": "fb_output_decls", "named": true}, {"type": "in_out_decls", "named": true}, {"type": "loc_partly_var_decl", "named": true}, {"type": "no_retain_var_decls", "named": true}, {"type": "retain_var_decls", "named": true}, {"type": "temp_var_decls", "named": true}, {"type": "var_decls", "named": true}]}, {"type": "_func_variables", "named": true, "subtypes": [{"type": "external_var_decls", "named": true}, {"type": "in_out_decls", "named": true}, {"type": "input_decls", "named": true}, {"type": "output_decls", "named": true}, {"type": "temp_var_decls", "named": true}, {"type": "var_decls", "named": true}]}, {"type": "_global_var_kind", "named": true, "subtypes": [{"type": "fq_name", "named": true}, {"type": "loc_var_spec_init", "named": true}]}, {"type": "_in_out_var_kind", "named": true, "subtypes": [{"type": "array_conformand", "named": true}, {"type": "fb_decl_no_init", "named": true}, {"type": "var_decl", "named": true}]}, {"type": "_input_var_kind", "named": true, "subtypes": [{"type": "array_conformand", "named": true}, {"type": "edge_decl", "named": true}, {"type": "var_decl_init", "named": true}]}, {"type": "_output_var_kind", "named": true, "subtypes": [{"type": "array_conformand", "named": true}, {"type": "var_decl_init", "named": true}]}, {"type": "_primary_expression", "named": true, "subtypes": [{"type": "constant", "named": true}, {"type": "enum_value", "named": true}, {"type": "fq_name", "named": true}, {"type": "func_call", "named": true}, {"type": "parenthesized_expression", "named": true}, {"type": "ref_value", "named": true}, {"type": "variable_access", "named": true}]}, {"type": "_temp_var_kind", "named": true, "subtypes": [{"type": "ref_spec", "named": true}, {"type": "var_decl", "named": true}]}, {"type": "add", "named": true, "subtypes": [{"type": "+", "named": false}, {"type": "-", "named": false}]}, {"type": "eq", "named": true, "subtypes": [{"type": "<>", "named": false}, {"type": "=", "named": false}]}, {"type": "mult", "named": true, "subtypes": [{"type": "*", "named": false}, {"type": "/", "named": false}, {"type": "MOD", "named": false}]}, {"type": "ord", "named": true, "subtypes": [{"type": "<", "named": false}, {"type": "<=", "named": false}, {"type": ">", "named": false}, {"type": ">=", "named": false}]}, {"type": "unary", "named": true, "subtypes": [{"type": "+", "named": false}, {"type": "-", "named": false}, {"type": "NOT", "named": false}]}, {"type": "SFC", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "SFC_network", "named": true}]}}, {"type": "SFC_network", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "action", "named": true}, {"type": "initial_step", "named": true}, {"type": "step", "named": true}, {"type": "transition", "named": true}]}}, {"type": "access_direction", "named": true, "fields": {}}, {"type": "access_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "access_spec", "named": true, "fields": {}}, {"type": "action", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "action_name", "named": true}, {"type": "fb_body", "named": true}]}}, {"type": "action_association", "named": true, "fields": {"variable_name": {"multiple": true, "required": false, "types": [{"type": "identifier", "named": true}]}}, "children": {"multiple": true, "required": true, "types": [{"type": "action_name", "named": true}, {"type": "action_qualifier", "named": true}]}}, {"type": "action_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "action_qualifier", "named": true, "fields": {}, "children": {"multiple": false, "required": false, "types": [{"type": "action_time", "named": true}]}}, {"type": "action_time", "named": true, "fields": {"variable_name": {"multiple": false, "required": false, "types": [{"type": "identifier", "named": true}]}}, "children": {"multiple": false, "required": false, "types": [{"type": "duration", "named": true}]}}, {"type": "add_operator", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}, "operator": {"multiple": false, "required": true, "types": [{"type": "add", "named": true}]}, "right": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}}}, {"type": "and_operator", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}, "right": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}}}, {"type": "array_conformand", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "_data_type_access", "named": true}]}}, {"type": "array_elem_init", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "array_elem_init_value", "named": true}, {"type": "unsigned_int", "named": true}]}}, {"type": "array_elem_init_value", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "array_type_init", "named": true}, {"type": "constant_expr", "named": true}, {"type": "struct_elem_init", "named": true}]}}, {"type": "array_type_init", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "array_elem_init", "named": true}]}}, {"type": "array_type_spec", "named": true, "fields": {"ranges": {"multiple": true, "required": true, "types": [{"type": ",", "named": false}, {"type": "subrange", "named": true}]}, "type": {"multiple": false, "required": true, "types": [{"type": "_data_type_access", "named": true}]}}}, {"type": "assign", "named": true, "fields": {"target": {"multiple": false, "required": true, "types": [{"type": "assignment", "named": true}, {"type": "assignment_attempt", "named": true}, {"type": "ref_assign", "named": true}]}, "variable": {"multiple": false, "required": true, "types": [{"type": "variable", "named": true}]}}}, {"type": "assignment", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}}, {"type": "assignment_attempt", "named": true, "fields": {"target": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}, {"type": "ref_deref", "named": true}, {"type": "ref_value", "named": true}]}}}, {"type": "binary_int", "named": true, "fields": {}}, {"type": "bit_str_type_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "bool_name", "named": true}, {"type": "multibits_type_name", "named": true}]}}, {"type": "bool_literal", "named": true, "fields": {"type": {"multiple": false, "required": false, "types": [{"type": "BOOL#", "named": false}]}, "value": {"multiple": false, "required": true, "types": [{"type": "FALSE", "named": false}, {"type": "TRUE", "named": false}]}}}, {"type": "boolean_operator", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "and_operator", "named": true}, {"type": "or_operator", "named": true}, {"type": "xor_operator", "named": true}]}}, {"type": "case_list", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "case_list_elem", "named": true}]}}, {"type": "case_list_elem", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "constant_expr", "named": true}, {"type": "subrange", "named": true}]}}, {"type": "case_selection", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "case_list", "named": true}, {"type": "stmt_list", "named": true}]}}, {"type": "case_stmt", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "_expression", "named": true}, {"type": "case_selection", "named": true}, {"type": "stmt_list", "named": true}]}}, {"type": "char_literal", "named": true, "fields": {"char": {"multiple": false, "required": true, "types": [{"type": "char_str", "named": true}]}}}, {"type": "char_str", "named": true, "fields": {}}, {"type": "class_decl", "named": true, "fields": {"declarations": {"multiple": true, "required": false, "types": [{"type": "_class_variables", "named": true}]}, "directives": {"multiple": true, "required": false, "types": [{"type": "using_directive", "named": true}]}, "extends": {"multiple": false, "required": false, "types": [{"type": "fq_name", "named": true}]}, "implements": {"multiple": false, "required": false, "types": [{"type": "interface_name_list", "named": true}]}, "methods": {"multiple": true, "required": false, "types": [{"type": "method_decl", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "class_type_name", "named": true}]}, "qualifier": {"multiple": false, "required": false, "types": [{"type": "ABSTRACT", "named": false}, {"type": "FINAL", "named": false}]}}}, {"type": "class_type_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "comment", "named": true, "fields": {}}, {"type": "comparison_operator", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "eq_operator", "named": true}, {"type": "ord_operator", "named": true}]}}, {"type": "config_decl", "named": true, "fields": {"global_variables": {"multiple": false, "required": false, "types": [{"type": "global_var_decls", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "config_name", "named": true}]}, "ressources": {"multiple": true, "required": true, "types": [{"type": "resource_decl", "named": true}, {"type": "single_resource_decl", "named": true}]}}}, {"type": "config_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "constant", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "bool_literal", "named": true}, {"type": "char_literal", "named": true}, {"type": "numeric_literal", "named": true}, {"type": "time_literal", "named": true}]}}, {"type": "constant_expr", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}}, {"type": "control_variable", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "d_byte_str_spec", "named": true, "fields": {"size": {"multiple": false, "required": false, "types": [{"type": "unsigned_int", "named": true}]}}}, {"type": "data_sink", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "direct_variable", "named": true}, {"type": "global_ref_deref", "named": true}]}}, {"type": "data_source", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "constant", "named": true}, {"type": "direct_variable", "named": true}, {"type": "global_ref_deref", "named": true}, {"type": "prog_output_access", "named": true}]}}, {"type": "data_type_decl", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "type_decl", "named": true}]}}, {"type": "date", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "long_date", "named": true}, {"type": "short_date", "named": true}]}}, {"type": "date_and_time", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "long_date_and_time", "named": true}, {"type": "short_date_and_time", "named": true}]}}, {"type": "date_type_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "date_name", "named": true}, {"type": "ldate_name", "named": true}]}}, {"type": "direct_variable", "named": true, "fields": {"kind": {"multiple": false, "required": true, "types": [{"type": "IQM", "named": true}]}, "offset": {"multiple": true, "required": true, "types": [{"type": ".", "named": false}, {"type": "unsigned_int", "named": true}]}, "size": {"multiple": false, "required": false, "types": [{"type": "XBWDL", "named": true}]}}}, {"type": "dt_type_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "dt_name", "named": true}, {"type": "ldt_name", "named": true}]}}, {"type": "duration", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "ltime", "named": true}, {"type": "time", "named": true}]}}, {"type": "edge_decl", "named": true, "fields": {"edge": {"multiple": false, "required": false, "types": [{"type": "F_EDGE", "named": false}, {"type": "R_EDGE", "named": false}]}, "variables": {"multiple": false, "required": true, "types": [{"type": "variable_list", "named": true}]}}}, {"type": "enum_spec", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "fq_name", "named": true}, {"type": "identifier", "named": true}]}}, {"type": "enum_type_spec", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "_elem_type_name", "named": true}, {"type": "enum_spec", "named": true}, {"type": "named_spec", "named": true}]}}, {"type": "enum_value", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "enum_value_spec", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "_expression", "named": true}, {"type": "identifier", "named": true}, {"type": "int_literal", "named": true}]}}, {"type": "eq_operator", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}, "operator": {"multiple": false, "required": true, "types": [{"type": "eq", "named": true}]}, "right": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}}}, {"type": "external_decl", "named": true, "fields": {"name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "type": {"multiple": false, "required": true, "types": [{"type": "_external_var_kind", "named": true}]}}}, {"type": "external_var_decls", "named": true, "fields": {"constant": {"multiple": false, "required": false, "types": [{"type": "CONSTANT", "named": false}]}}, "children": {"multiple": true, "required": false, "types": [{"type": "external_decl", "named": true}]}}, {"type": "fb_body", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "SFC", "named": true}, {"type": "fb_diagram", "named": true}, {"type": "ladder_diagram", "named": true}, {"type": "stmt_list", "named": true}]}}, {"type": "fb_decl", "named": true, "fields": {"body": {"multiple": false, "required": false, "types": [{"type": "fb_body", "named": true}]}, "directives": {"multiple": true, "required": false, "types": [{"type": "using_directive", "named": true}]}, "extends": {"multiple": false, "required": false, "types": [{"type": "fq_name", "named": true}]}, "implements": {"multiple": false, "required": false, "types": [{"type": "interface_name_list", "named": true}]}, "method": {"multiple": true, "required": false, "types": [{"type": "method_decl", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "qualifier": {"multiple": false, "required": false, "types": [{"type": "ABSTRACT", "named": false}, {"type": "FINAL", "named": false}]}, "variables": {"multiple": true, "required": false, "types": [{"type": "_fb_variables", "named": true}]}}}, {"type": "fb_decl_no_init", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "fb_name", "named": true}, {"type": "fq_name", "named": true}]}}, {"type": "fb_diagram", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "fbd_network", "named": true}]}}, {"type": "fb_input_decls", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "fb_input_var", "named": true}]}}, {"type": "fb_input_var", "named": true, "fields": {"type": {"multiple": false, "required": true, "types": [{"type": "_fb_input_var_kind", "named": true}]}, "variables": {"multiple": false, "required": true, "types": [{"type": "variable_list", "named": true}]}}}, {"type": "fb_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "fb_output_decls", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "fb_output_var", "named": true}]}}, {"type": "fb_output_var", "named": true, "fields": {"type": {"multiple": false, "required": true, "types": [{"type": "_fb_output_var_kind", "named": true}]}, "variables": {"multiple": false, "required": true, "types": [{"type": "variable_list", "named": true}]}}}, {"type": "fb_task", "named": true, "fields": {"task": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, "children": {"multiple": false, "required": true, "types": [{"type": "instance_name", "named": true}]}}, {"type": "for_list", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "_expression", "named": true}]}}, {"type": "for_stmt", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "control_variable", "named": true}, {"type": "for_list", "named": true}, {"type": "stmt_list", "named": true}]}}, {"type": "fq_name", "named": true, "fields": {"fragment": {"multiple": true, "required": false, "types": [{"type": "identifier", "named": true}]}, "target": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}}, {"type": "func_body", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "fb_diagram", "named": true}, {"type": "ladder_diagram", "named": true}, {"type": "stmt_list", "named": true}]}}, {"type": "func_call", "named": true, "fields": {"params": {"multiple": true, "required": false, "types": [{"type": ",", "named": false}, {"type": "param_assign", "named": true}]}, "target": {"multiple": false, "required": true, "types": [{"type": "fq_name", "named": true}]}}}, {"type": "func_decl", "named": true, "fields": {"access": {"multiple": true, "required": false, "types": [{"type": ":", "named": false}, {"type": "_data_type_access", "named": true}]}, "body": {"multiple": false, "required": false, "types": [{"type": "func_body", "named": true}]}, "directives": {"multiple": true, "required": false, "types": [{"type": "using_directive", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "variables": {"multiple": true, "required": false, "types": [{"type": "_func_variables", "named": true}]}}}, {"type": "global_ref_deref", "named": true, "fields": {"name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "ressource": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "struct": {"multiple": false, "required": false, "types": [{"type": "identifier", "named": true}]}}}, {"type": "global_var_decl", "named": true, "fields": {"spec": {"multiple": false, "required": true, "types": [{"type": "global_var_spec", "named": true}]}, "type": {"multiple": false, "required": true, "types": [{"type": "_global_var_kind", "named": true}]}}}, {"type": "global_var_decls", "named": true, "fields": {"constant_or_retain": {"multiple": false, "required": false, "types": [{"type": "CONSTANT", "named": false}, {"type": "RETAIN", "named": false}]}}, "children": {"multiple": true, "required": false, "types": [{"type": "global_var_decl", "named": true}]}}, {"type": "global_var_spec", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "identifier", "named": true}, {"type": "located_at", "named": true}]}}, {"type": "hex_int", "named": true, "fields": {"value": {"multiple": true, "required": true, "types": [{"type": "_", "named": false}]}}}, {"type": "if_stmt", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "_expression", "named": true}, {"type": "stmt_list", "named": true}]}}, {"type": "in_out_decls", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "in_out_var", "named": true}]}}, {"type": "in_out_var", "named": true, "fields": {"type": {"multiple": false, "required": true, "types": [{"type": "_in_out_var_kind", "named": true}]}, "variables": {"multiple": false, "required": true, "types": [{"type": "variable_list", "named": true}]}}}, {"type": "initial_step", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "action_association", "named": true}, {"type": "step_name", "named": true}]}}, {"type": "input_decls", "named": true, "fields": {"retain": {"multiple": false, "required": false, "types": [{"type": "NON_RETAIN", "named": false}, {"type": "RETAIN", "named": false}]}}, "children": {"multiple": true, "required": false, "types": [{"type": "input_var", "named": true}]}}, {"type": "input_var", "named": true, "fields": {"type": {"multiple": false, "required": true, "types": [{"type": "_input_var_kind", "named": true}]}, "variables": {"multiple": false, "required": true, "types": [{"type": "variable_list", "named": true}]}}}, {"type": "instance_name", "named": true, "fields": {"name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}}, {"type": "int_kind", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "int_type_name", "named": true}, {"type": "multibits_type_name", "named": true}]}}, {"type": "int_literal", "named": true, "fields": {"int": {"multiple": false, "required": true, "types": [{"type": "binary_int", "named": true}, {"type": "hex_int", "named": true}, {"type": "octal_int", "named": true}, {"type": "signed_int", "named": true}]}, "kind": {"multiple": false, "required": false, "types": [{"type": "int_kind", "named": true}]}}}, {"type": "int_type_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "sign_int_type_name", "named": true}, {"type": "unsign_int_type_name", "named": true}]}}, {"type": "interface_decl", "named": true, "fields": {"directives": {"multiple": true, "required": false, "types": [{"type": "using_directive", "named": true}]}, "extends": {"multiple": false, "required": false, "types": [{"type": "interface_name_list", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "prototype": {"multiple": true, "required": false, "types": [{"type": "method_prototype", "named": true}]}}}, {"type": "interface_name_list", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "fq_name", "named": true}]}}, {"type": "invocation", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "instance_name", "named": true}, {"type": "param_assign", "named": true}, {"type": "this", "named": true}, {"type": "this_invocation", "named": true}]}}, {"type": "l_real", "named": true, "fields": {"value": {"multiple": false, "required": true, "types": [{"type": "real_value", "named": true}]}}}, {"type": "ladder_diagram", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "ld_rung", "named": true}]}}, {"type": "loc_partly_var", "named": true, "fields": {"variable_name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, "children": {"multiple": true, "required": true, "types": [{"type": "IQM", "named": true}, {"type": "var_spec", "named": true}]}}, {"type": "loc_partly_var_decl", "named": true, "fields": {"retain": {"multiple": false, "required": false, "types": [{"type": "NON_RETAIN", "named": false}, {"type": "RETAIN", "named": false}]}}, "children": {"multiple": true, "required": false, "types": [{"type": "loc_partly_var", "named": true}]}}, {"type": "loc_var_decl", "named": true, "fields": {"variable_name": {"multiple": false, "required": false, "types": [{"type": "identifier", "named": true}]}}, "children": {"multiple": true, "required": true, "types": [{"type": "loc_var_spec_init", "named": true}, {"type": "located_at", "named": true}]}}, {"type": "loc_var_decls", "named": true, "fields": {"constant_or_retain": {"multiple": false, "required": false, "types": [{"type": "CONSTANT", "named": false}, {"type": "NON_RETAIN", "named": false}, {"type": "RETAIN", "named": false}]}}, "children": {"multiple": true, "required": false, "types": [{"type": "loc_var_decl", "named": true}]}}, {"type": "loc_var_spec_init", "named": true, "fields": {"init": {"multiple": false, "required": false, "types": [{"type": "array_type_init", "named": true}, {"type": "simple_type_init", "named": true}, {"type": "struct_type_init", "named": true}]}, "spec": {"multiple": false, "required": true, "types": [{"type": "array_type_spec", "named": true}, {"type": "simple_type_spec", "named": true}, {"type": "str_type_spec", "named": true}, {"type": "struct_type_spec", "named": true}]}}}, {"type": "located_at", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "direct_variable", "named": true}]}}, {"type": "long_date", "named": true, "fields": {"value": {"multiple": false, "required": true, "types": [{"type": "date_literal", "named": true}]}}}, {"type": "long_date_and_time", "named": true, "fields": {"value": {"multiple": false, "required": true, "types": [{"type": "date_and_daytime", "named": true}]}}}, {"type": "ltime", "named": true, "fields": {"sign": {"multiple": false, "required": false, "types": [{"type": "+", "named": false}, {"type": "-", "named": false}]}, "value": {"multiple": false, "required": true, "types": [{"type": "time_value", "named": true}]}}}, {"type": "ltod", "named": true, "fields": {"value": {"multiple": false, "required": true, "types": [{"type": "daytime", "named": true}]}}}, {"type": "method_decl", "named": true, "fields": {"body": {"multiple": false, "required": false, "types": [{"type": "func_body", "named": true}]}}, "children": {"multiple": true, "required": true, "types": [{"type": "_data_type_access", "named": true}, {"type": "access_spec", "named": true}, {"type": "external_var_decls", "named": true}, {"type": "identifier", "named": true}, {"type": "in_out_decls", "named": true}, {"type": "input_decls", "named": true}, {"type": "output_decls", "named": true}, {"type": "temp_var_decls", "named": true}, {"type": "var_decls", "named": true}]}}, {"type": "method_prototype", "named": true, "fields": {"data_type": {"multiple": false, "required": false, "types": [{"type": "_data_type_access", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "variables": {"multiple": true, "required": false, "types": [{"type": "in_out_decls", "named": true}, {"type": "input_decls", "named": true}, {"type": "output_decls", "named": true}]}}}, {"type": "mult_operator", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}, "operator": {"multiple": false, "required": true, "types": [{"type": "mult", "named": true}]}, "right": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}}}, {"type": "multi_elem_var", "named": true, "fields": {"access": {"multiple": false, "required": true, "types": [{"type": "var_access", "named": true}]}}, "children": {"multiple": true, "required": true, "types": [{"type": "struct_variable", "named": true}, {"type": "subscript_list", "named": true}]}}, {"type": "multibit_part_access", "named": true, "fields": {"size": {"multiple": false, "required": false, "types": [{"type": "XBWDL", "named": true}]}}, "children": {"multiple": false, "required": true, "types": [{"type": "unsigned_int", "named": true}]}}, {"type": "multibits_type_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "byte_name", "named": true}, {"type": "dword_name", "named": true}, {"type": "lword_name", "named": true}, {"type": "word_name", "named": true}]}}, {"type": "named_spec", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "enum_value_spec", "named": true}]}}, {"type": "namespace_decl", "named": true, "fields": {"directives": {"multiple": true, "required": false, "types": [{"type": "using_directive", "named": true}]}, "elements": {"multiple": false, "required": false, "types": [{"type": "namespace_elements", "named": true}]}, "internal": {"multiple": false, "required": false, "types": [{"type": "INTERNAL", "named": false}]}, "name": {"multiple": false, "required": true, "types": [{"type": "namespace_h_name", "named": true}]}}}, {"type": "namespace_elements", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "class_decl", "named": true}, {"type": "data_type_decl", "named": true}, {"type": "fb_decl", "named": true}, {"type": "func_decl", "named": true}, {"type": "interface_decl", "named": true}, {"type": "namespace_decl", "named": true}]}}, {"type": "namespace_h_name", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "no_retain_var_decls", "named": true, "fields": {"spec": {"multiple": false, "required": false, "types": [{"type": "access_spec", "named": true}]}}, "children": {"multiple": true, "required": false, "types": [{"type": "var_decl_init_list", "named": true}]}}, {"type": "numeric_literal", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "int_literal", "named": true}, {"type": "real_literal", "named": true}]}}, {"type": "numeric_type_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "int_type_name", "named": true}, {"type": "real_type_name", "named": true}]}}, {"type": "octal_int", "named": true, "fields": {"value": {"multiple": true, "required": true, "types": [{"type": "_", "named": false}]}}}, {"type": "or_operator", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}, "right": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}}}, {"type": "ord_operator", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}, "operator": {"multiple": false, "required": true, "types": [{"type": "ord", "named": true}]}, "right": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}}}, {"type": "output_decls", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "output_var", "named": true}]}}, {"type": "output_var", "named": true, "fields": {"type": {"multiple": false, "required": true, "types": [{"type": "_output_var_kind", "named": true}]}, "variables": {"multiple": false, "required": true, "types": [{"type": "variable_list", "named": true}]}}}, {"type": "param_assign", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "param_assign_input", "named": true}, {"type": "param_assign_output", "named": true}]}}, {"type": "param_assign_input", "named": true, "fields": {"param": {"multiple": false, "required": false, "types": [{"type": "identifier", "named": true}]}, "value": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}}}, {"type": "param_assign_output", "named": true, "fields": {"not": {"multiple": false, "required": false, "types": [{"type": "NOT", "named": false}]}, "param": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "variable": {"multiple": false, "required": true, "types": [{"type": "variable", "named": true}]}}}, {"type": "parenthesized_expression", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}}, {"type": "power_operator", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}, "right": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}}}, {"type": "prog_access_decl", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "_data_type_access", "named": true}, {"type": "access_direction", "named": true}, {"type": "access_name", "named": true}, {"type": "multibit_part_access", "named": true}, {"type": "symbolic_variable", "named": true}]}}, {"type": "prog_cnxn", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "data_sink", "named": true}, {"type": "prog_data_source", "named": true}, {"type": "symbolic_variable", "named": true}]}}, {"type": "prog_conf_elem", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "fb_task", "named": true}, {"type": "prog_cnxn", "named": true}]}}, {"type": "prog_conf_elems", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "prog_conf_elem", "named": true}]}}, {"type": "prog_config", "named": true, "fields": {"access": {"multiple": false, "required": true, "types": [{"type": "prog_type_access", "named": true}]}, "configuration_elements": {"multiple": true, "required": false, "types": [{"type": "(", "named": false}, {"type": ")", "named": false}, {"type": "prog_conf_elems", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "retain": {"multiple": false, "required": false, "types": [{"type": "NON_RETAIN", "named": false}, {"type": "RETAIN", "named": false}]}, "task": {"multiple": true, "required": false, "types": [{"type": "WITH", "named": false}, {"type": "identifier", "named": true}]}}}, {"type": "prog_data_source", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "constant", "named": true}, {"type": "direct_variable", "named": true}, {"type": "enum_value", "named": true}, {"type": "global_ref_deref", "named": true}]}}, {"type": "prog_decl", "named": true, "fields": {"body": {"multiple": false, "required": false, "types": [{"type": "fb_body", "named": true}]}, "declarations": {"multiple": true, "required": false, "types": [{"type": "external_var_decls", "named": true}, {"type": "in_out_decls", "named": true}, {"type": "input_decls", "named": true}, {"type": "loc_partly_var_decl", "named": true}, {"type": "loc_var_decls", "named": true}, {"type": "no_retain_var_decls", "named": true}, {"type": "output_decls", "named": true}, {"type": "prog_access_decl", "named": true}, {"type": "retain_var_decls", "named": true}, {"type": "temp_var_decls", "named": true}, {"type": "var_decls", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}}, {"type": "prog_output_access", "named": true, "fields": {"prog_name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, "children": {"multiple": false, "required": true, "types": [{"type": "symbolic_variable", "named": true}]}}, {"type": "prog_type_access", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "fq_name", "named": true}]}}, {"type": "real", "named": true, "fields": {"value": {"multiple": false, "required": true, "types": [{"type": "real_value", "named": true}]}}}, {"type": "real_literal", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "l_real", "named": true}, {"type": "real", "named": true}]}}, {"type": "real_type_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "lreal_name", "named": true}, {"type": "real_name", "named": true}]}}, {"type": "real_value", "named": true, "fields": {}}, {"type": "ref_addr", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "instance_name", "named": true}, {"type": "symbolic_variable", "named": true}]}}, {"type": "ref_assign", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "ref_deref", "named": true}]}}, {"type": "ref_deref", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "ref_spec", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "_data_type_access", "named": true}]}}, {"type": "ref_type_spec", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "_data_type_access", "named": true}]}}, {"type": "ref_value", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "null", "named": true}, {"type": "ref_addr", "named": true}]}}, {"type": "repeat_stmt", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "_expression", "named": true}, {"type": "stmt_list", "named": true}]}}, {"type": "resource_decl", "named": true, "fields": {"global_variables": {"multiple": false, "required": false, "types": [{"type": "global_var_decls", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "resource_type_name": {"multiple": false, "required": true, "types": [{"type": "resource_type_name", "named": true}]}, "ressource": {"multiple": false, "required": true, "types": [{"type": "single_resource_decl", "named": true}]}}}, {"type": "resource_type_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "retain_var_decls", "named": true, "fields": {"access": {"multiple": false, "required": false, "types": [{"type": "access_spec", "named": true}]}, "retain": {"multiple": false, "required": true, "types": [{"type": "RETAIN", "named": false}]}}, "children": {"multiple": true, "required": false, "types": [{"type": "var_decl_init_list", "named": true}]}}, {"type": "s_byte_str_spec", "named": true, "fields": {"size": {"multiple": false, "required": false, "types": [{"type": "unsigned_int", "named": true}]}}}, {"type": "short_date", "named": true, "fields": {"value": {"multiple": false, "required": true, "types": [{"type": "date_literal", "named": true}]}}}, {"type": "short_date_and_time", "named": true, "fields": {"value": {"multiple": false, "required": true, "types": [{"type": "date_and_daytime", "named": true}]}}}, {"type": "sign_int_type_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "dint_name", "named": true}, {"type": "int_name", "named": true}, {"type": "lint_name", "named": true}, {"type": "sint_name", "named": true}]}}, {"type": "signed_int", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "unsigned_int", "named": true}]}}, {"type": "simple_type_init", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "constant_expr", "named": true}]}}, {"type": "simple_type_spec", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "_elem_type_name", "named": true}]}}, {"type": "single_resource_decl", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "prog_config", "named": true}, {"type": "task_config", "named": true}]}}, {"type": "source_file", "named": true, "root": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "class_decl", "named": true}, {"type": "config_decl", "named": true}, {"type": "data_type_decl", "named": true}, {"type": "fb_decl", "named": true}, {"type": "func_decl", "named": true}, {"type": "interface_decl", "named": true}, {"type": "namespace_decl", "named": true}, {"type": "prog_decl", "named": true}]}}, {"type": "step", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "action_association", "named": true}, {"type": "step_name", "named": true}]}}, {"type": "step_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "steps", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "step_name", "named": true}]}}, {"type": "stmt", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "assign", "named": true}, {"type": "case_stmt", "named": true}, {"type": "for_stmt", "named": true}, {"type": "func_call", "named": true}, {"type": "if_stmt", "named": true}, {"type": "invocation", "named": true}, {"type": "repeat_stmt", "named": true}, {"type": "super", "named": true}, {"type": "while_stmt", "named": true}]}}, {"type": "stmt_list", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "stmt", "named": true}]}}, {"type": "str_type_spec", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "d_byte_str_spec", "named": true}, {"type": "d_char", "named": true}, {"type": "s_byte_str_spec", "named": true}, {"type": "s_char", "named": true}]}}, {"type": "struct_elem_decl", "named": true, "fields": {"init": {"multiple": false, "required": false, "types": [{"type": "array_type_init", "named": true}, {"type": "simple_type_init", "named": true}, {"type": "struct_type_init", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "spec": {"multiple": false, "required": true, "types": [{"type": "array_type_spec", "named": true}, {"type": "enum_type_spec", "named": true}, {"type": "simple_type_spec", "named": true}, {"type": "struct_type_spec", "named": true}, {"type": "subrange_type_spec", "named": true}]}}, "children": {"multiple": true, "required": false, "types": [{"type": "located_at", "named": true}, {"type": "multibit_part_access", "named": true}]}}, {"type": "struct_elem_init", "named": true, "fields": {"name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, "children": {"multiple": false, "required": true, "types": [{"type": "array_type_init", "named": true}, {"type": "constant_expr", "named": true}, {"type": "struct_init", "named": true}]}}, {"type": "struct_init", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "struct_elem_init", "named": true}]}}, {"type": "struct_type_init", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "struct_elem_init", "named": true}]}}, {"type": "struct_type_spec", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "struct_elem_decl", "named": true}]}}, {"type": "struct_variable", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "var_access", "named": true}]}}, {"type": "subrange", "named": true, "fields": {"lower": {"multiple": false, "required": true, "types": [{"type": "constant_expr", "named": true}]}, "upper": {"multiple": false, "required": true, "types": [{"type": "constant_expr", "named": true}]}}}, {"type": "subrange_type_spec", "named": true, "fields": {"range": {"multiple": false, "required": true, "types": [{"type": "subrange", "named": true}]}, "type": {"multiple": false, "required": true, "types": [{"type": "int_type_name", "named": true}]}}}, {"type": "subscript_list", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "_expression", "named": true}]}}, {"type": "symbolic_variable", "named": true, "fields": {"this": {"multiple": true, "required": false, "types": [{"type": "this", "named": true}]}}, "children": {"multiple": false, "required": true, "types": [{"type": "multi_elem_var", "named": true}, {"type": "var_access", "named": true}]}}, {"type": "task_config", "named": true, "fields": {"init": {"multiple": false, "required": true, "types": [{"type": "task_init", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}}, {"type": "task_init", "named": true, "fields": {"interval": {"multiple": false, "required": false, "types": [{"type": "data_source", "named": true}]}, "priority": {"multiple": false, "required": true, "types": [{"type": "unsigned_int", "named": true}]}, "single": {"multiple": false, "required": false, "types": [{"type": "data_source", "named": true}]}}}, {"type": "temp_var", "named": true, "fields": {"type": {"multiple": false, "required": true, "types": [{"type": "_temp_var_kind", "named": true}]}, "variables": {"multiple": false, "required": true, "types": [{"type": "variable_list", "named": true}]}}}, {"type": "temp_var_decls", "named": true, "fields": {}, "children": {"multiple": true, "required": false, "types": [{"type": "temp_var", "named": true}]}}, {"type": "this_invocation", "named": true, "fields": {"params": {"multiple": true, "required": false, "types": [{"type": ",", "named": false}, {"type": "param_assign", "named": true}]}}, "children": {"multiple": true, "required": true, "types": [{"type": "instance_name", "named": true}, {"type": "method_name", "named": true}]}}, {"type": "time", "named": true, "fields": {"sign": {"multiple": false, "required": false, "types": [{"type": "+", "named": false}, {"type": "-", "named": false}]}, "value": {"multiple": false, "required": true, "types": [{"type": "time_value", "named": true}]}}}, {"type": "time_literal", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "date", "named": true}, {"type": "date_and_time", "named": true}, {"type": "duration", "named": true}, {"type": "time_of_day", "named": true}]}}, {"type": "time_of_day", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "ltod", "named": true}, {"type": "tod", "named": true}]}}, {"type": "time_type_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "ltime_name", "named": true}, {"type": "time_name", "named": true}]}}, {"type": "tod", "named": true, "fields": {"value": {"multiple": false, "required": true, "types": [{"type": "daytime", "named": true}]}}}, {"type": "tod_type_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "ltod_name", "named": true}, {"type": "tod_name", "named": true}]}}, {"type": "transition", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "steps", "named": true}, {"type": "transition_cond", "named": true}, {"type": "transition_name", "named": true}, {"type": "unsigned_int", "named": true}]}}, {"type": "transition_cond", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}, {"type": "fbd_network", "named": true}, {"type": "ld_rung", "named": true}]}}, {"type": "transition_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "type_decl", "named": true, "fields": {"init": {"multiple": false, "required": false, "types": [{"type": "array_type_init", "named": true}, {"type": "simple_type_init", "named": true}, {"type": "struct_type_init", "named": true}]}, "name": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}]}, "spec": {"multiple": false, "required": true, "types": [{"type": "array_type_spec", "named": true}, {"type": "enum_type_spec", "named": true}, {"type": "ref_type_spec", "named": true}, {"type": "simple_type_spec", "named": true}, {"type": "str_type_spec", "named": true}, {"type": "struct_type_spec", "named": true}, {"type": "subrange_type_spec", "named": true}]}}}, {"type": "unary_operator", "named": true, "fields": {"expr": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}, "operator": {"multiple": false, "required": true, "types": [{"type": "unary", "named": true}]}}}, {"type": "unsign_int_type_name", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "udint_name", "named": true}, {"type": "uint_name", "named": true}, {"type": "ulint_name", "named": true}, {"type": "usint_name", "named": true}]}}, {"type": "unsigned_int", "named": true, "fields": {}}, {"type": "using_directive", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "namespace_h_name", "named": true}]}}, {"type": "var_access", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "identifier", "named": true}, {"type": "ref_deref", "named": true}]}}, {"type": "var_decl", "named": true, "fields": {"init": {"multiple": false, "required": false, "types": [{"type": "array_type_init", "named": true}, {"type": "simple_type_init", "named": true}, {"type": "struct_type_init", "named": true}]}, "spec": {"multiple": false, "required": true, "types": [{"type": "array_type_spec", "named": true}, {"type": "simple_type_spec", "named": true}, {"type": "str_type_spec", "named": true}, {"type": "struct_type_spec", "named": true}]}}}, {"type": "var_decl_init", "named": true, "fields": {"init": {"multiple": false, "required": false, "types": [{"type": "array_type_init", "named": true}, {"type": "simple_type_init", "named": true}, {"type": "struct_type_init", "named": true}]}, "spec": {"multiple": false, "required": true, "types": [{"type": "array_type_spec", "named": true}, {"type": "simple_type_spec", "named": true}, {"type": "str_type_spec", "named": true}, {"type": "struct_type_spec", "named": true}]}}}, {"type": "var_decl_init_list", "named": true, "fields": {"type": {"multiple": false, "required": true, "types": [{"type": "var_decl", "named": true}]}, "variables": {"multiple": false, "required": true, "types": [{"type": "variable_list", "named": true}]}}}, {"type": "var_decls", "named": true, "fields": {"access": {"multiple": false, "required": false, "types": [{"type": "access_spec", "named": true}]}, "constant": {"multiple": false, "required": false, "types": [{"type": "CONSTANT", "named": false}]}}, "children": {"multiple": true, "required": false, "types": [{"type": "var_decl_init_list", "named": true}]}}, {"type": "var_spec", "named": true, "fields": {}, "children": {"multiple": false, "required": false, "types": [{"type": "array_type_spec", "named": true}, {"type": "fq_name", "named": true}, {"type": "unsigned_int", "named": true}]}}, {"type": "variable", "named": true, "fields": {}, "children": {"multiple": false, "required": true, "types": [{"type": "direct_variable", "named": true}, {"type": "symbolic_variable", "named": true}]}}, {"type": "variable_access", "named": true, "fields": {"access": {"multiple": false, "required": true, "types": [{"type": "multibit_part_access", "named": true}]}, "variable": {"multiple": false, "required": true, "types": [{"type": "variable", "named": true}]}}}, {"type": "variable_list", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "identifier", "named": true}]}}, {"type": "while_stmt", "named": true, "fields": {}, "children": {"multiple": true, "required": true, "types": [{"type": "_primary_expression", "named": true}, {"type": "stmt_list", "named": true}]}}, {"type": "xor_operator", "named": true, "fields": {"left": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}, "right": {"multiple": false, "required": true, "types": [{"type": "_expression", "named": true}]}}}, {"type": " ", "named": false}, {"type": "!", "named": false}, {"type": "\"", "named": false}, {"type": "#", "named": false}, {"type": "$", "named": false}, {"type": "$\"", "named": false}, {"type": "$$", "named": false}, {"type": "$'", "named": false}, {"type": "$L", "named": false}, {"type": "$N", "named": false}, {"type": "$P", "named": false}, {"type": "$R", "named": false}, {"type": "$T", "named": false}, {"type": "%", "named": false}, {"type": "&", "named": false}, {"type": "'", "named": false}, {"type": "(", "named": false}, {"type": "(*", "named": false}, {"type": ")", "named": false}, {"type": "*", "named": false}, {"type": "*)", "named": false}, {"type": "**", "named": false}, {"type": "*/", "named": false}, {"type": "+", "named": false}, {"type": ",", "named": false}, {"type": "-", "named": false}, {"type": ".", "named": false}, {"type": "..", "named": false}, {"type": "/", "named": false}, {"type": "/*", "named": false}, {"type": "//", "named": false}, {"type": "0", "named": false}, {"type": "1", "named": false}, {"type": "16#", "named": false}, {"type": "2", "named": false}, {"type": "2#", "named": false}, {"type": "3", "named": false}, {"type": "4", "named": false}, {"type": "5", "named": false}, {"type": "6", "named": false}, {"type": "7", "named": false}, {"type": "8", "named": false}, {"type": "8#", "named": false}, {"type": "9", "named": false}, {"type": ":", "named": false}, {"type": "::", "named": false}, {"type": ":=", "named": false}, {"type": ";", "named": false}, {"type": "<", "named": false}, {"type": "<=", "named": false}, {"type": "<>", "named": false}, {"type": "=", "named": false}, {"type": "=>", "named": false}, {"type": ">", "named": false}, {"type": ">=", "named": false}, {"type": "?", "named": false}, {"type": "?=", "named": false}, {"type": "@", "named": false}, {"type": "A", "named": false}, {"type": "ABSTRACT", "named": false}, {"type": "ACTION", "named": false}, {"type": "AND", "named": false}, {"type": "ARRAY", "named": false}, {"type": "AT", "named": false}, {"type": "B", "named": false}, {"type": "BOOL", "named": false}, {"type": "BOOL#", "named": false}, {"type": "BY", "named": false}, {"type": "C", "named": false}, {"type": "CASE", "named": false}, {"type": "CLASS", "named": false}, {"type": "CONFIGURATION", "named": false}, {"type": "CONSTANT", "named": false}, {"type": "CONTINUE", "named": false}, {"type": "D", "named": false}, {"type": "DATE", "named": false}, {"type": "DATE_AND_TIME", "named": false}, {"type": "DO", "named": false}, {"type": "DS", "named": false}, {"type": "DT", "named": false}, {"type": "E", "named": false}, {"type": "ELSE", "named": false}, {"type": "ELSE IF", "named": false}, {"type": "END_ACTION", "named": false}, {"type": "END_CASE", "named": false}, {"type": "END_CLASS", "named": false}, {"type": "END_CONFIGURATION", "named": false}, {"type": "END_FOR", "named": false}, {"type": "END_FUNCTION", "named": false}, {"type": "END_FUNCTION_BLOCK", "named": false}, {"type": "END_IF", "named": false}, {"type": "END_INTERFACE", "named": false}, {"type": "END_METHOD", "named": false}, {"type": "END_NAMESPACE", "named": false}, {"type": "END_PROGRAM", "named": false}, {"type": "END_REPEAT", "named": false}, {"type": "END_RESOURCE", "named": false}, {"type": "END_STEP", "named": false}, {"type": "END_STRUCT", "named": false}, {"type": "END_TRANSITION", "named": false}, {"type": "END_TYPE", "named": false}, {"type": "END_VAR", "named": false}, {"type": "END_WHILE", "named": false}, {"type": "EXIT", "named": false}, {"type": "EXTENDS", "named": false}, {"type": "F", "named": false}, {"type": "FALSE", "named": false}, {"type": "FINAL", "named": false}, {"type": "FOR", "named": false}, {"type": "FROM", "named": false}, {"type": "FUNCTION", "named": false}, {"type": "FUNCTION_BLOCK", "named": false}, {"type": "F_EDGE", "named": false}, {"type": "G", "named": false}, {"type": "H", "named": false}, {"type": "I", "named": false}, {"type": "IF", "named": false}, {"type": "IMPLEMENTS", "named": false}, {"type": "INITIAL_STEP", "named": false}, {"type": "INTERFACE", "named": false}, {"type": "INTERNAL", "named": false}, {"type": "INTERVAL", "named": false}, {"type": "J", "named": false}, {"type": "K", "named": false}, {"type": "L", "named": false}, {"type": "LD", "named": false}, {"type": "LDATE", "named": false}, {"type": "LDATE_AND_TIME", "named": false}, {"type": "LDT", "named": false}, {"type": "LREAL", "named": false}, {"type": "LT", "named": false}, {"type": "LTIME", "named": false}, {"type": "LTIME_OF_DAY", "named": false}, {"type": "LTOD", "named": false}, {"type": "M", "named": false}, {"type": "METHOD", "named": false}, {"type": "MOD", "named": false}, {"type": "N", "named": false}, {"type": "NAMESPACE", "named": false}, {"type": "NON_RETAIN", "named": false}, {"type": "NOT", "named": false}, {"type": "O", "named": false}, {"type": "OF", "named": false}, {"type": "ON", "named": false}, {"type": "OR", "named": false}, {"type": "OVERLAP", "named": false}, {"type": "OVERRIDE", "named": false}, {"type": "P", "named": false}, {"type": "PRIORITY", "named": false}, {"type": "PRIVATE", "named": false}, {"type": "PROGRAM", "named": false}, {"type": "PROTECTED", "named": false}, {"type": "PUBLIC", "named": false}, {"type": "Q", "named": false}, {"type": "R", "named": false}, {"type": "READ_ONLY", "named": false}, {"type": "READ_WRITE", "named": false}, {"type": "REAL", "named": false}, {"type": "REF", "named": false}, {"type": "REF_TO", "named": false}, {"type": "REPEAT", "named": false}, {"type": "RESOURCE", "named": false}, {"type": "RETAIN", "named": false}, {"type": "RETURN", "named": false}, {"type": "R_EDGE", "named": false}, {"type": "S", "named": false}, {"type": "SD", "named": false}, {"type": "SINGLE", "named": false}, {"type": "SL", "named": false}, {"type": "STEP", "named": false}, {"type": "STRING", "named": false}, {"type": "STRING#", "named": false}, {"type": "STRUCT", "named": false}, {"type": "T", "named": false}, {"type": "TASK", "named": false}, {"type": "THEN", "named": false}, {"type": "THIS", "named": false}, {"type": "TIME", "named": false}, {"type": "TIME_OF_DAY", "named": false}, {"type": "TO", "named": false}, {"type": "TOD", "named": false}, {"type": "TRANSITION", "named": false}, {"type": "TRUE", "named": false}, {"type": "TYPE", "named": false}, {"type": "U", "named": false}, {"type": "UNTIL", "named": false}, {"type": "USING", "named": false}, {"type": "V", "named": false}, {"type": "VAR", "named": false}, {"type": "VAR_EXTERNAL", "named": false}, {"type": "VAR_GLOBAL", "named": false}, {"type": "VAR_INPUT", "named": false}, {"type": "VAR_IN_OUT", "named": false}, {"type": "VAR_OUTPUT", "named": false}, {"type": "VAR_TEMP", "named": false}, {"type": "W", "named": false}, {"type": "WHILE", "named": false}, {"type": "WITH", "named": false}, {"type": "WSTRING", "named": false}, {"type": "X", "named": false}, {"type": "XOR", "named": false}, {"type": "Y", "named": false}, {"type": "Z", "named": false}, {"type": "[", "named": false}, {"type": "\\", "named": false}, {"type": "]", "named": false}, {"type": "^", "named": false}, {"type": "_", "named": false}, {"type": "`", "named": false}, {"type": "a", "named": false}, {"type": "b", "named": false}, {"type": "bool_name", "named": true}, {"type": "byte_name", "named": true}, {"type": "c", "named": false}, {"type": "d", "named": false}, {"type": "d_char", "named": true}, {"type": "date_and_daytime", "named": true}, {"type": "date_literal", "named": true}, {"type": "date_name", "named": true}, {"type": "daytime", "named": true}, {"type": "dint_name", "named": true}, {"type": "dt_name", "named": true}, {"type": "dword_name", "named": true}, {"type": "e", "named": false}, {"type": "f", "named": false}, {"type": "fbd_network", "named": true}, {"type": "g", "named": false}, {"type": "h", "named": false}, {"type": "i", "named": false}, {"type": "identifier", "named": true}, {"type": "int_name", "named": true}, {"type": "j", "named": false}, {"type": "k", "named": false}, {"type": "l", "named": false}, {"type": "ld_rung", "named": true}, {"type": "ldate_name", "named": true}, {"type": "ldt_name", "named": true}, {"type": "lint_name", "named": true}, {"type": "lreal_name", "named": true}, {"type": "ltime_name", "named": true}, {"type": "ltod_name", "named": true}, {"type": "lword_name", "named": true}, {"type": "m", "named": false}, {"type": "method_name", "named": true}, {"type": "n", "named": false}, {"type": "null", "named": true}, {"type": "o", "named": false}, {"type": "p", "named": false}, {"type": "q", "named": false}, {"type": "r", "named": false}, {"type": "real_name", "named": true}, {"type": "s", "named": false}, {"type": "s_char", "named": true}, {"type": "sint_name", "named": true}, {"type": "super", "named": true}, {"type": "t", "named": false}, {"type": "this", "named": true}, {"type": "time_name", "named": true}, {"type": "time_value", "named": true}, {"type": "tod_name", "named": true}, {"type": "u", "named": false}, {"type": "udint_name", "named": true}, {"type": "uint_name", "named": true}, {"type": "ulint_name", "named": true}, {"type": "usint_name", "named": true}, {"type": "v", "named": false}, {"type": "w", "named": false}, {"type": "word_name", "named": true}, {"type": "x", "named": false}, {"type": "y", "named": false}, {"type": "z", "named": false}, {"type": "{", "named": false}, {"type": "|", "named": false}, {"type": "}", "named": false}, {"type": "~", "named": false}]