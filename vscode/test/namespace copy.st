NAMESPACE System
	USING System
	NAMESPACE Subsystem END_NAMESPACE
	NAMESPACE Subsystem
		

		NAMESPACE INTERNAL Main 
			INTERFACE ンズ・ゲーンズoom2 
				METHOD DAYTIME END_METHOD 
					// in night-time 
				METHOD NIGHTTIME: INT
					VAR_INPUT
						// Called in day-time
						input1,
						input2: INT;
					END_VAR 

				END_METHOD
			END_INTERFACE
			FUNCTION fn : BOOL 
			
			END_FUNCTION
			FUNCTION fn : BOOL 
			
			END_FUNCTION
			FUNCTION_BLOCK Main2 IMPLEMENTS Room
				VAR_INPUT
					input1,
					input2: INT;
				END_VAR
			END_FUNCTION_BLOCK
		END_NAMESPACE
	END_NAMESPACE
END_NAMESPACE
