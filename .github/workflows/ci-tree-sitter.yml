name: tree-sitter

on:
  push:
    branches: [main]
    paths:
      - crates/tree-sitter/grammar.js
      - crates/tree-sitter/src/**
      - crates/tree-sitter/bindings/**
      - crates/tree-sitter/binding.gyp
  pull_request:
    paths:
      - crates/tree-sitter/grammar.js
      - crates/tree-sitter/src/**
      - crates/tree-sitter/bindings/**
      - crates/tree-sitter/binding.gyp

jobs:
  test:
    name: Test parser
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v4

      - name: Setup Tree-sitter CL<PERSON>
        uses: tree-sitter/setup-action@v2

      - name: Test parser
        working-directory: crates/tree-sitter
        run: tree-sitter test
