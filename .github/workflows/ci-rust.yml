name: rust
on:
  push:
    paths-ignore:
      - "**.md"
      - "**.js"
      - "**.ts"
  pull_request:
    paths-ignore:
      - "**.md"
      - "**.js"
      - "**.ts"

jobs:
  test:
    runs-on: ubuntu-latest
    env:
      CARGO_TERM_COLOR: always
    steps:
      - name: Install nextest
        uses: taiki-e/install-action@nextest
      - uses: actions/checkout@v4
      - uses: Swatinem/rust-cache@v2
      - name: Test workspace
        run: cargo-nextest ntr --workspace